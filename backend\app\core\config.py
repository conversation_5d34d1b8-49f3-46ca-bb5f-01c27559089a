"""
Configuration settings for NeuroV CRM backend.

This module provides centralized configuration management using Pydantic settings.
Environment variables are loaded from .env file and validated.
"""

from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # Database Configuration
    DATABASE_URL: Optional[str] = None

    # Supabase Configuration
    SUPABASE_URL: Optional[str] = None
    SUPABASE_SERVICE_KEY: Optional[str] = None
    SUPABASE_ANON_KEY: Optional[str] = None
    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = None
    SUPABASE_JWT_SECRET: Optional[str] = None  # Required for JWT signature verification

    # Google OAuth Configuration
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None
    GOOGLE_REDIRECT_URI: str = "http://localhost:8000/api/v1/calendar/oauth/callback"

    # Security Configuration
    JWT_SECRET_KEY: Optional[str] = None
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRATION_HOURS: int = 24
    TOKEN_ENCRYPTION_KEY: Optional[str] = None

    # API Configuration
    API_PREFIX: str = "/api/v1"
    DEBUG: bool = True
    FRONTEND_URL: str = "http://localhost:3000"

    # Calendar Configuration
    CALENDAR_SYNC_INTERVAL_MINUTES: int = 15
    MAX_CALENDAR_EVENTS_PER_SYNC: int = 100

    # Redis Configuration for Cache Synchronization
    REDIS_HOST: Optional[str] = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: Optional[str] = None

    # Twilio Configuration
    TWILIO_ACCOUNT_SID: Optional[str] = None
    TWILIO_AUTH_TOKEN: Optional[str] = None
    PRIMARY_CUSTOMER_PROFILE_SID: Optional[str] = None
    TWILIO_TEST_MODE: str = "false"

    # Provisioning Configuration
    MAX_PROVISIONING_RETRIES: int = 3
    PROVISIONING_RETRY_DELAY_BASE: int = 2

    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"  # 'json' or 'text'
    LOG_FILE_MAX_SIZE: int = ********  # 10MB in bytes
    LOG_FILE_BACKUP_COUNT: int = 5

    # Admin Configuration
    ADMIN_EMAILS: str = "<EMAIL>"

    # Webhook Configuration
    SUPABASE_WEBHOOK_SECRET: Optional[str] = None
    WEBHOOK_BASE_URL: Optional[str] = None

    # Email Configuration
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAIL_FROM_ADDRESS: str = "<EMAIL>"

    # Trust Hub Polling Configuration
    TRUST_HUB_POLLING_ENABLED: bool = False  # Disabled by default - rely on webhooks
    TRUST_HUB_POLLING_INTERVAL: int = 3600   # 1 hour if enabled (reduced from 5 minutes)

    @property
    def admin_email_list(self) -> List[str]:
        """Get list of admin emails from comma-separated string."""
        return [email.strip() for email in self.ADMIN_EMAILS.split(',')]

    @property
    def webhook_base_url(self) -> str:
        """Get environment-appropriate webhook base URL."""
        if self.WEBHOOK_BASE_URL:
            return self.WEBHOOK_BASE_URL

        # Environment-dependent defaults
        if self.DEBUG:
            return "https://localhost:8000"
        else:
            return "https://api.neurov.ai"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


# Global settings instance
settings = Settings()