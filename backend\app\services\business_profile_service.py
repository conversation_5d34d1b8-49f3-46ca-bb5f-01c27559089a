"""
Business Profile Service for TARA A2P Configuration

This service handles business profile operations including:
- CRUD operations for business profile data
- Twilio Trust Hub integration (Customer Profile, Brand Registration)
- Cost estimation and tracking
- Status synchronization and webhook handling
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from decimal import Decimal
from pydantic import ValidationError

from app.db.supabase import get_supabase_admin, get_supabase_user_authenticated
from app.services.twilio_data_mappers import map_registration_id_type_to_twilio
from app.schemas.tara import (
    BusinessProfileRequest,
    BusinessProfileData,
    BusinessProfileStatus,
    BrandRegistrationStatus,
    BusinessType,
    BusinessRegistrationIdentifier,
    BusinessIdentity,
    TaraVolumeTier,
    CostBreakdown,
    AutoSaveBusinessProfileRequest
)
from app.exceptions.tara_exceptions import TaraDatabaseError, TaraServiceError
from app.services.auto_save_cache import auto_save_cache_service, _calculate_data_hash
from app.services.cost_tracking_service import cost_tracking_service
from app.services.sandbox_service import sandbox_service
from app.services.twilio_customer_profiles_service import TwilioCustomerProfilesService
from app.services.twilio_brand_registration_service import TwilioBrandRegistrationService
from app.services.business_profile_database_operations import BusinessProfileDatabaseOperations

logger = logging.getLogger(__name__)

class BusinessProfileService:
    """Service for managing business profile operations"""

    def __init__(self, jwt_token: Optional[str] = None):
        """
        Initialize business profile service with Supabase client and Twilio services

        Args:
            jwt_token: Optional JWT token for user-authenticated operations.
                      If provided, uses user-authenticated client that respects RLS.
                      If None, uses admin client (for backward compatibility).
        """
        if jwt_token:
            self.supabase = get_supabase_user_authenticated(jwt_token)
            self.db_ops = BusinessProfileDatabaseOperations(jwt_token=jwt_token)
        else:
            # Backward compatibility: use admin client if no JWT token provided
            self.supabase = get_supabase_admin()
            self.db_ops = BusinessProfileDatabaseOperations()

        self.customer_profiles_service = TwilioCustomerProfilesService()
        self.brand_registration_service = TwilioBrandRegistrationService()
    
    async def get_user_business_profile(self, user_id: str) -> Optional[BusinessProfileData]:
        """
        Get user's business profile data

        Args:
            user_id: Supabase auth user ID

        Returns:
            BusinessProfileData if found, None otherwise

        Raises:
            TaraDatabaseError: If database operation fails
        """
        try:
            logger.debug(f"Getting business profile for user: {user_id}")

            # Get user's subaccount context
            subaccount_context = await self._get_user_subaccount_context(user_id)
            if not subaccount_context:
                logger.warning(f"No subaccount found for user: {user_id}")
                return None

            subaccount_sid = subaccount_context["subaccount_sid"]

            # Query business profile data from trust_hub_customer_profiles table
            # Include both old and new field names to ensure data retrieval
            response = await asyncio.to_thread(
                lambda: self.supabase.table("trust_hub_customer_profiles")
                .select("""
                    business_name,
                    business_type,
                    business_registration_number,
                    business_registration_id_type,
                    business_registration_id,
                    business_identity,
                    website_url,
                    business_industry,
                    business_regions,
                    business_regions_of_operation,
                    social_media_profile_urls,
                    street,
                    street_secondary,
                    street_address,
                    city,
                    state_province,
                    postal_code,
                    country,
                    rep_first_name,
                    rep_last_name,
                    rep_email,
                    rep_phone,
                    rep_job_position,
                    rep_business_title,
                    representative_first_name,
                    representative_last_name,
                    representative_email,
                    representative_phone,
                    representative_job_title,
                    status,
                    customer_profile_sid,
                    submitted_for_review_at,
                    approved_at,
                    rejected_at,
                    date_updated
                """)
                .eq("subaccount_sid", subaccount_sid)
                .order("date_updated", desc=True)
                .limit(1)
                .execute()
            )
            
            if not response.data:
                logger.info(f"No business profile found for user: {user_id}")
                return None

            profile_data = response.data[0]
            logger.debug(f"Retrieved business profile data for user {user_id}: {profile_data}")

            # Convert database data to BusinessProfileData model
            business_profile = self._convert_db_to_business_profile(profile_data)

            logger.info(f"Successfully retrieved business profile for user: {user_id}")
            return business_profile

        except ValidationError as e:
            # Handle validation errors gracefully - this should be rare now with improved conversion
            logger.warning(f"Validation error for user {user_id}, returning empty template: {e}")
            return self.create_empty_profile_template()
        except Exception as e:
            logger.error(f"Error getting business profile for user {user_id}: {e}")
            raise TaraDatabaseError(f"Failed to retrieve business profile: {str(e)}")
    
    async def save_business_profile(self, user_id: str, profile_request: BusinessProfileRequest) -> BusinessProfileData:
        """
        Save or create business profile
        
        Args:
            user_id: Supabase auth user ID
            profile_request: Business profile data to save
            
        Returns:
            BusinessProfileData: Saved business profile data
            
        Raises:
            TaraServiceError: If save operation fails
        """
        try:
            logger.info(f"Saving business profile for user: {user_id}")
            
            # Get user's subaccount context
            subaccount_context = await self._get_user_subaccount_context(user_id)
            if not subaccount_context:
                raise TaraServiceError("User subaccount not found. Complete provisioning first.")
            
            subaccount_sid = subaccount_context["subaccount_sid"]

            # Check if user already has a customer profile with real Bundle SID from Twilio
            existing_profile = await self._get_existing_customer_profile(subaccount_sid)

            # Only use existing Bundle SID if it exists from a previous Twilio API call
            # For draft profiles, these fields should be NULL until Twilio API success
            if existing_profile and existing_profile.get("bundle_sid"):
                customer_profile_sid = existing_profile["bundle_sid"]
                bundle_sid = existing_profile["bundle_sid"]
            else:
                # For draft profiles without Twilio submission, keep these fields NULL
                customer_profile_sid = None
                bundle_sid = None

            # Prepare upsert data for trust_hub_customer_profiles table
            # Note: Using new Twilio Trust Hub fields with backward compatibility
            upsert_data = {
                "customer_profile_sid": customer_profile_sid,
                "bundle_sid": bundle_sid,  # Ensure Bundle SID is always set
                "subaccount_sid": subaccount_sid,
                "business_name": profile_request.company_name,
                "business_type": profile_request.business_type.value,
                # Use Twilio Trust Hub official field names
                "business_registration_number": profile_request.business_registration_number,
                "business_registration_id_type": profile_request.business_registration_id_type.value,
                # Store the Twilio-mapped value for API compatibility
                "business_registration_identifier": map_registration_id_type_to_twilio(
                    profile_request.business_registration_id_type.value
                ),
                "business_identity": (
                    profile_request.business_identity.value
                    if profile_request.business_identity
                    else "direct_customer"
                ),
                "website_url": profile_request.website,
                "business_industry": profile_request.vertical,
                "business_regions": profile_request.business_regions,
                "business_regions_of_operation": profile_request.business_regions_of_operation,
                "social_media_profile_urls": profile_request.social_media_profile_urls,
                "street": profile_request.address.street,
                "street_secondary": profile_request.address.street_secondary,
                "city": profile_request.address.city,
                "state_province": profile_request.address.state,
                "postal_code": profile_request.address.postal_code,
                "country": profile_request.address.country,
                "rep_first_name": profile_request.representative.first_name,
                "rep_last_name": profile_request.representative.last_name,
                "rep_email": profile_request.representative.email,
                "rep_phone": profile_request.representative.phone,
                "rep_job_position": profile_request.representative.job_title,
                "rep_business_title": profile_request.representative.business_title,
                "status": "draft",
                "profile_type": "secondary",
                "created_by": user_id,
                "updated_at": datetime.utcnow().isoformat()
            }

            # Use proper UPSERT with subaccount_sid as primary key
            # This will update existing record or insert new one
            response = await asyncio.to_thread(
                lambda: self.supabase.table("trust_hub_customer_profiles")
                .upsert(upsert_data, on_conflict="subaccount_sid")
                .execute()
            )
            
            if not response.data:
                raise TaraServiceError("Failed to save business profile")
            
            # Convert saved data back to BusinessProfileData
            saved_data = response.data[0]
            business_profile = self._convert_db_to_business_profile(saved_data)
            
            logger.info(f"Successfully saved business profile for user: {user_id}")
            return business_profile
            
        except TaraServiceError:
            raise
        except Exception as e:
            logger.error(f"Error saving business profile for user {user_id}: {e}")
            raise TaraServiceError(f"Failed to save business profile: {str(e)}")
    
    async def estimate_costs(self, user_id: str, business_type: BusinessType, volume_tier: TaraVolumeTier,
                           business_regions: List[str], include_campaign: bool = True) -> Dict[str, Any]:
        """
        Estimate costs for business profile and brand registration using enhanced cost tracking service

        Args:
            user_id: Supabase auth user ID
            business_type: Type of business entity
            volume_tier: Expected volume tier
            business_regions: Business operating regions
            include_campaign: Whether to include campaign registration costs

        Returns:
            Dict containing cost breakdown and additional information
        """
        try:
            logger.debug(f"Estimating costs for user {user_id}: {business_type.value}, {volume_tier.value}, regions: {business_regions}")

            # Get user's subaccount context
            subaccount_context = await self._get_user_subaccount_context(user_id)
            if not subaccount_context:
                raise TaraServiceError("User subaccount not found. Complete provisioning first.")

            subaccount_sid = subaccount_context["subaccount_sid"]

            # Check if sandbox mode is enabled
            if sandbox_service.is_sandbox_enabled():
                logger.info(f"Using sandbox mode for cost estimation (mode: {sandbox_service.get_sandbox_mode().value})")
                cost_estimation = await sandbox_service.mock_cost_calculation(
                    business_type=business_type,
                    volume_tier=volume_tier,
                    business_regions=business_regions
                )
                # Add sandbox warnings
                cost_estimation = sandbox_service.add_sandbox_warnings(cost_estimation)
            else:
                # Use enhanced cost tracking service for production
                cost_estimation = await cost_tracking_service.estimate_business_profile_costs(
                    subaccount_sid=subaccount_sid,
                    business_type=business_type,
                    volume_tier=volume_tier,
                    business_regions=business_regions,
                    include_campaign=include_campaign
                )

            # Create cost acceptance workflow if costs are involved
            if cost_estimation["total_cost"] > 0:
                workflow = await cost_tracking_service.create_cost_acceptance_workflow(
                    subaccount_sid=subaccount_sid,
                    workflow_type="business_profile_creation",
                    cost_breakdown=cost_estimation["cost_breakdown"],
                    total_cost=cost_estimation["total_cost"]
                )

                cost_estimation["workflow"] = workflow

            return cost_estimation

        except TaraServiceError:
            raise
        except Exception as e:
            logger.error(f"Error estimating costs for user {user_id}: {e}")
            raise TaraServiceError(f"Failed to estimate costs: {str(e)}")

    async def auto_save_business_profile(self, user_id: str, profile_request: AutoSaveBusinessProfileRequest) -> Dict[str, Any]:
        """
        Auto-save business profile with partial data and debouncing

        Args:
            user_id: Supabase auth user ID
            profile_request: Partial business profile data to auto-save

        Returns:
            Dict containing auto-save metadata and status

        Raises:
            TaraServiceError: If auto-save operation fails
        """
        try:
            logger.debug(f"Auto-saving business profile for user: {user_id}")

            # Calculate data hash for debouncing
            data_dict = profile_request.dict(exclude_none=True)
            data_hash = _calculate_data_hash(data_dict)
            session_id = profile_request.client_session_id or f"session_{user_id}"

            # Check if auto-save should proceed (debouncing logic)
            if not auto_save_cache_service.should_auto_save(user_id, session_id, data_hash):
                # Return cached stats without saving
                cache_stats = auto_save_cache_service.get_auto_save_stats(user_id, session_id)
                return {
                    "profile_id": f"BU{user_id[2:12] if len(user_id) > 12 else user_id}",
                    "status": "debounced",
                    "completion_percentage": 0,  # Will be calculated if needed
                    "auto_saved_at": cache_stats["last_auto_save"] if cache_stats else datetime.utcnow().isoformat(),
                    "client_session_id": session_id,
                    "next_auto_save_in": int(cache_stats["next_auto_save_allowed_in"]) if cache_stats else 0,
                    "debounced": True
                }

            # Get user's subaccount context
            subaccount_context = await self._get_user_subaccount_context(user_id)
            if not subaccount_context:
                raise TaraServiceError("User subaccount not found. Complete provisioning first.")

            subaccount_sid = subaccount_context["subaccount_sid"]

            # Check if user already has a customer profile with real Bundle SID from Twilio
            existing_profile_data = await self._get_existing_customer_profile(subaccount_sid)

            # Only use existing Bundle SID if it exists from a previous Twilio API call
            # For draft profiles, these fields should be NULL until Twilio API success
            if existing_profile_data and existing_profile_data.get("bundle_sid"):
                customer_profile_sid = existing_profile_data["bundle_sid"]
                bundle_sid = existing_profile_data["bundle_sid"]
            else:
                # For draft profiles without Twilio submission, keep these fields NULL
                # as requested by the user - no placeholder values
                customer_profile_sid = None
                bundle_sid = None

            # Get existing profile data to merge with auto-save data
            existing_profile = await self.get_user_business_profile(user_id)

            # Prepare partial upsert data for trust_hub_customer_profiles table
            upsert_data = {
                "customer_profile_sid": customer_profile_sid,
                "bundle_sid": bundle_sid,  # Ensure Bundle SID is always set
                "subaccount_sid": subaccount_sid,
                "status": "draft",
                "profile_type": "secondary",
                "updated_at": datetime.utcnow().isoformat(),
                "created_by": user_id
            }

            # Add only non-None fields from the auto-save request
            # Use enhanced field mapping logic consistent with main business profile service
            if profile_request.company_name is not None:
                upsert_data["business_name"] = profile_request.company_name
            if profile_request.business_type is not None:
                upsert_data["business_type"] = profile_request.business_type.value

            # Registration number handling - store only in newer field name
            if profile_request.business_registration_id is not None:
                # Store only in newer field name for forward compatibility
                upsert_data["business_registration_number"] = profile_request.business_registration_id
            elif profile_request.ein is not None:
                # Legacy support for ein field - store in newer field name
                upsert_data["business_registration_number"] = profile_request.ein
            # Handle registration ID type (new field)
            if profile_request.business_registration_id_type is not None:
                # Validate registration ID type against database constraint
                allowed_types = ["US_EIN", "AU_ABN", "EIN", "DUNS", "CCN", "CBN", "CN", "ACN", "CIN", "VAT", "VATRN", "RN", "Other"]
                reg_id_type_value = profile_request.business_registration_id_type.value
                if reg_id_type_value not in allowed_types:
                    logger.warning(f"Invalid registration ID type: {reg_id_type_value}. Allowed: {allowed_types}")
                else:
                    upsert_data["business_registration_id_type"] = reg_id_type_value
                    # Store the Twilio-mapped value for API compatibility
                    upsert_data["business_registration_identifier"] = map_registration_id_type_to_twilio(reg_id_type_value)
            # Handle legacy field for backward compatibility
            elif profile_request.registration_id_type is not None:
                # Legacy support for registration_id_type field
                allowed_types = ["US_EIN", "AU_ABN", "EIN", "DUNS", "CCN", "CBN", "CN", "ACN", "CIN", "VAT", "VATRN", "RN", "Other"]
                if profile_request.registration_id_type not in allowed_types:
                    logger.warning(f"Invalid registration ID type: {profile_request.registration_id_type}. Allowed: {allowed_types}")
                else:
                    upsert_data["business_registration_id_type"] = profile_request.registration_id_type
                    # Store the Twilio-mapped value for API compatibility
                    upsert_data["business_registration_identifier"] = map_registration_id_type_to_twilio(profile_request.registration_id_type)
            if profile_request.website is not None:
                upsert_data["website_url"] = profile_request.website
            if profile_request.vertical is not None:
                upsert_data["business_industry"] = profile_request.vertical
            if profile_request.business_regions is not None:
                upsert_data["business_regions"] = profile_request.business_regions

            # Address fields handling - store only in newer field names
            if profile_request.address is not None:
                if profile_request.address.street is not None:
                    # Store only in newer field name for forward compatibility
                    upsert_data["street_address"] = profile_request.address.street
                if profile_request.address.city is not None:
                    upsert_data["city"] = profile_request.address.city
                if profile_request.address.state is not None:
                    upsert_data["state_province"] = profile_request.address.state
                if profile_request.address.postal_code is not None:
                    upsert_data["postal_code"] = profile_request.address.postal_code
                if profile_request.address.country is not None:
                    upsert_data["country"] = profile_request.address.country

            # Representative fields handling - store only in newer field names
            if profile_request.representative is not None:
                if profile_request.representative.first_name is not None:
                    # Store only in newer field name for forward compatibility
                    upsert_data["representative_first_name"] = profile_request.representative.first_name
                if profile_request.representative.last_name is not None:
                    upsert_data["representative_last_name"] = profile_request.representative.last_name
                if profile_request.representative.email is not None:
                    upsert_data["representative_email"] = profile_request.representative.email
                if profile_request.representative.phone is not None:
                    upsert_data["representative_phone"] = profile_request.representative.phone
                if profile_request.representative.job_title is not None:
                    upsert_data["representative_job_title"] = profile_request.representative.job_title
                if profile_request.representative.business_title is not None:
                    # Note: rep_business_title only exists in legacy format, so we keep it
                    upsert_data["rep_business_title"] = profile_request.representative.business_title

            # Use proper UPSERT with subaccount_sid as primary key for auto-save
            try:
                response = await asyncio.to_thread(
                    lambda: self.supabase.table("trust_hub_customer_profiles")
                    .upsert(upsert_data, on_conflict="subaccount_sid")
                    .execute()
                )

                if not response.data:
                    raise TaraServiceError("Failed to auto-save business profile - no data returned")

            except Exception as db_error:
                error_msg = str(db_error).lower()
                if "constraint" in error_msg or "check" in error_msg:
                    logger.error(f"Database constraint violation during auto-save: {db_error}")
                    raise TaraServiceError(f"Data validation failed: Invalid registration ID type or other constraint violation")
                else:
                    logger.error(f"Database error during auto-save: {db_error}")
                    raise TaraServiceError(f"Database error during auto-save: {str(db_error)}")

            # Calculate completion percentage and verify critical fields were saved
            saved_data = response.data[0]

            # Verify critical fields were actually persisted (detect silent constraint failures)
            if profile_request.business_registration_id_type is not None:
                saved_reg_type = saved_data.get("business_registration_id_type")
                expected_value = profile_request.business_registration_id_type.value
                if not saved_reg_type:
                    logger.error(f"Registration ID type failed to save: sent '{expected_value}' but got NULL in database")
                elif saved_reg_type != expected_value:
                    logger.warning(f"Registration ID type mismatch: sent '{expected_value}' but saved '{saved_reg_type}'")
            elif profile_request.registration_id_type is not None:
                # Legacy field verification
                saved_reg_type = saved_data.get("business_registration_id_type")
                expected_value = profile_request.registration_id_type
                if not saved_reg_type:
                    logger.error(f"Registration ID type failed to save: sent '{expected_value}' but got NULL in database")
                elif saved_reg_type != expected_value:
                    logger.warning(f"Registration ID type mismatch: sent '{expected_value}' but saved '{saved_reg_type}'")

            completion_percentage = self._calculate_completion_percentage(saved_data)

            auto_save_result = {
                "profile_id": customer_profile_sid,
                "status": "draft",
                "completion_percentage": completion_percentage,
                "auto_saved_at": datetime.utcnow().isoformat(),
                "client_session_id": profile_request.client_session_id,
                "next_auto_save_in": 60  # 1 minute (60 seconds)
            }

            logger.info(f"Successfully auto-saved business profile for user: {user_id} ({completion_percentage}% complete)")
            return auto_save_result

        except TaraServiceError:
            raise
        except Exception as e:
            logger.error(f"Error auto-saving business profile for user {user_id}: {e}")
            raise TaraServiceError(f"Failed to auto-save business profile: {str(e)}")

    def _calculate_completion_percentage(self, profile_data: Dict[str, Any]) -> int:
        """Calculate completion percentage based on required fields for trust_hub_customer_profiles"""
        required_fields = [
            "business_name", "business_type", "business_registration_number", "website_url", "business_industry",
            "street", "city", "state_province", "postal_code", "country",
            "rep_first_name", "rep_last_name", "rep_email", "rep_phone", "rep_job_position"
        ]

        completed_fields = 0
        for field in required_fields:
            if profile_data.get(field) and str(profile_data.get(field)).strip():
                completed_fields += 1

        return int((completed_fields / len(required_fields)) * 100)

    def _is_empty_profile(self, db_data: Dict[str, Any]) -> bool:
        """
        Check if the business profile is essentially empty/new

        Args:
            db_data: Database record data from trust_hub_customer_profiles table

        Returns:
            True if profile is empty/new, False if it has meaningful data
        """
        # If no data at all, it's empty
        if not db_data:
            return True

        # Check essential fields that indicate a real profile (updated column names)
        essential_fields = ["business_name", "business_type", "business_registration_number", "website_url", "business_industry"]

        # If any essential field has meaningful data, it's not empty
        for field in essential_fields:
            value = db_data.get(field)
            if value and str(value).strip() and value != "" and value is not None:
                logger.debug(f"Found non-empty essential field {field}: {value}")
                return False

        # Check if address has any meaningful data (updated column names)
        address_fields = ["street", "city", "state_province", "postal_code"]
        for field in address_fields:
            value = db_data.get(field)
            if value and str(value).strip() and value != "" and value is not None:
                logger.debug(f"Found non-empty address field {field}: {value}")
                return False

        # Check if representative has any meaningful data (updated column names)
        rep_fields = ["rep_first_name", "rep_last_name", "rep_email", "rep_phone"]
        for field in rep_fields:
            value = db_data.get(field)
            if value and str(value).strip() and value != "" and value is not None:
                logger.debug(f"Found non-empty representative field {field}: {value}")
                return False

        logger.debug(f"Profile determined to be empty. Data keys: {list(db_data.keys())}")
        return True

    def create_empty_profile_template(self) -> BusinessProfileData:
        """
        Create a valid but empty business profile template

        Returns:
            BusinessProfileData with valid defaults that pass validation
        """
        from app.schemas.tara import BusinessAddressModel, BusinessRepresentativeModel

        # Create empty address and representative with None values
        address = BusinessAddressModel(
            street=None,
            city=None,
            state=None,
            postal_code=None,
            country=None
        )

        # Create empty representative with None values
        representative = BusinessRepresentativeModel(
            first_name=None,
            last_name=None,
            email=None,
            phone=None,
            job_title=None
        )

        return BusinessProfileData(
            company_name=None,
            business_type=None,
            ein=None,
            website=None,
            vertical=None,
            business_regions=None,
            address=address,
            representative=representative,
            status=BusinessProfileStatus.DRAFT,
            customer_profile_sid=None,
            brand_registration_sid=None,
            brand_registration_status=BrandRegistrationStatus.NOT_STARTED,
            completion_percentage=0,
            created_at=datetime.utcnow().isoformat(),
            updated_at=datetime.utcnow().isoformat()
        )

    async def _get_user_subaccount_context(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user's subaccount context using three-tier architecture"""
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("user_subaccount_mapping")
                .select("subaccount_sid")
                .eq("auth_user_id", user_id)
                .execute()
            )
            
            if not response.data:
                return None
            
            return {"subaccount_sid": response.data[0]["subaccount_sid"]}
            
        except Exception as e:
            logger.error(f"Error getting user subaccount context: {e}")
            raise TaraDatabaseError(f"Failed to get user context: {str(e)}")
    
    def _convert_db_to_business_profile(self, db_data: Dict[str, Any]) -> BusinessProfileData:
        """Convert database row from trust_hub_customer_profiles to BusinessProfileData model"""
        from app.schemas.tara import (
            BusinessAddressModel,
            BusinessRepresentativeModel,
            BusinessType,
            BusinessProfileStatus,
            BrandRegistrationStatus,
            BusinessRegistrationIdentifier,
            BusinessIdentity
        )
        from datetime import datetime

        # Check if this is an empty profile and return empty values if so
        if self._is_empty_profile(db_data):
            logger.info("Converting empty business profile, returning empty values")

            # Create empty address and representative objects with None values
            empty_address = BusinessAddressModel(
                street=None,
                street_secondary=None,
                city=None,
                state=None,
                postal_code=None,
                country=None
            )

            empty_representative = BusinessRepresentativeModel(
                first_name=None,
                last_name=None,
                email=None,
                phone=None,
                job_title=None,
                business_title=None
            )

            return BusinessProfileData(
                company_name=None,
                business_type=None,
                business_registration_id_type=None,
                business_registration_number=None,
                business_identity=None,
                website=None,
                vertical=None,
                business_regions=None,
                business_regions_of_operation=None,
                social_media_profile_urls=None,
                address=empty_address,
                representative=empty_representative,
                status=BusinessProfileStatus.DRAFT,
                customer_profile_sid=None,
                brand_registration_sid=None,
                ein=""  # Legacy field for backward compatibility
            )

        # Handle partial data with None values for missing fields - prefer newer field names
        street_address = db_data.get("street_address") or db_data.get("street")
        address = BusinessAddressModel(
            street=street_address,
            street_secondary=db_data.get("street_secondary"),
            city=db_data.get("city"),
            state=db_data.get("state_province"),
            postal_code=db_data.get("postal_code"),
            country=db_data.get("country")
        )

        # Create representative model with proper field mapping - prefer newer field names
        rep_first_name = db_data.get("representative_first_name") or db_data.get("rep_first_name")
        rep_last_name = db_data.get("representative_last_name") or db_data.get("rep_last_name")
        rep_email = db_data.get("representative_email") or db_data.get("rep_email")
        rep_phone = db_data.get("representative_phone") or db_data.get("rep_phone")
        rep_job_title = db_data.get("representative_job_title") or db_data.get("rep_job_position")
        rep_business_title = db_data.get("rep_business_title")  # Only exists in old format

        representative = BusinessRepresentativeModel(
            first_name=rep_first_name,
            last_name=rep_last_name,
            email=rep_email,
            phone=rep_phone,
            job_title=rep_job_title,
            business_title=rep_business_title
        )

        # Get registration data with None handling - prefer newer field names
        reg_id_type = db_data.get("business_registration_id_type") or db_data.get("business_registration_identifier")
        reg_number = db_data.get("business_registration_number") or db_data.get("business_registration_id")

        return BusinessProfileData(
            company_name=db_data.get("business_name"),
            business_type=BusinessType(db_data.get("business_type")) if db_data.get("business_type") else None,
            business_registration_id_type=BusinessRegistrationIdentifier(reg_id_type) if reg_id_type else None,
            business_registration_number=reg_number,
            business_identity=BusinessIdentity(db_data.get("business_identity")) if db_data.get("business_identity") else None,
            website=db_data.get("website_url"),
            vertical=db_data.get("business_industry"),
            business_regions=db_data.get("business_regions"),
            business_regions_of_operation=db_data.get("business_regions_of_operation"),
            social_media_profile_urls=db_data.get("social_media_profile_urls"),
            address=address,
            representative=representative,
            status=BusinessProfileStatus(db_data.get("status")) if db_data.get("status") else BusinessProfileStatus.DRAFT,
            customer_profile_sid=db_data.get("customer_profile_sid"),
            brand_registration_sid=None,  # Not stored in trust_hub_customer_profiles
            ein=db_data.get("business_registration_number")  # Legacy field for backward compatibility
        )

    async def create_customer_profile(self, user_id: str) -> Dict[str, Any]:
        """
        Create Twilio Customer Profile for user's business profile

        Args:
            user_id: Supabase auth user ID

        Returns:
            Dict containing customer profile creation results

        Raises:
            TaraServiceError: If customer profile creation fails
        """
        try:
            logger.info(f"Creating customer profile for user: {user_id}")

            # Get user's business profile
            business_profile = await self.get_user_business_profile(user_id)
            if not business_profile:
                raise TaraServiceError("Business profile not found. Please save your business profile first.")

            # Get user's subaccount context
            subaccount_context = await self._get_user_subaccount_context(user_id)
            if not subaccount_context:
                raise TaraServiceError("User subaccount not found. Complete provisioning first.")

            subaccount_sid = subaccount_context["subaccount_sid"]

            # Create customer profile via Twilio service
            customer_profile_result = await self.customer_profiles_service.create_customer_profile(
                business_profile, subaccount_sid
            )

            # Store complete customer profile data with all resource SIDs
            await self.db_ops.store_customer_profile_resources(
                subaccount_sid, customer_profile_result
            )

            logger.info(f"Successfully created customer profile for user: {user_id}")
            return customer_profile_result

        except TaraServiceError:
            raise
        except Exception as e:
            logger.error(f"Error creating customer profile for user {user_id}: {e}")
            raise TaraServiceError(f"Failed to create customer profile: {str(e)}")

    async def create_brand_registration(self, user_id: str, volume_tier: TaraVolumeTier = TaraVolumeTier.LESS_THAN_600) -> Dict[str, Any]:
        """
        Create Twilio Brand Registration for user's business profile

        Args:
            user_id: Supabase auth user ID
            volume_tier: Expected volume tier for brand type determination

        Returns:
            Dict containing brand registration creation results

        Raises:
            TaraServiceError: If brand registration creation fails
        """
        try:
            logger.info(f"Creating brand registration for user: {user_id}")

            # Get user's business profile
            business_profile = await self.get_user_business_profile(user_id)
            if not business_profile:
                raise TaraServiceError("Business profile not found. Please save your business profile first.")

            # Check if customer profile is approved
            if not business_profile.customer_profile_sid:
                raise TaraServiceError("Customer profile not found. Please create customer profile first.")

            if business_profile.status != BusinessProfileStatus.APPROVED:
                raise TaraServiceError("Customer profile must be approved before creating brand registration.")

            # Get user's subaccount context
            subaccount_context = await self._get_user_subaccount_context(user_id)
            if not subaccount_context:
                raise TaraServiceError("User subaccount not found. Complete provisioning first.")

            subaccount_sid = subaccount_context["subaccount_sid"]

            # Create trust product first
            trust_product_sid = await self.brand_registration_service.create_trust_product(
                business_profile, business_profile.customer_profile_sid
            )

            # Create brand registration
            brand_registration_result = await self.brand_registration_service.create_brand_registration(
                business_profile,
                business_profile.customer_profile_sid,
                trust_product_sid,
                volume_tier
            )

            # Update database with brand registration SID and status
            await self.db_ops.update_brand_registration_status(
                subaccount_sid,
                BrandRegistrationStatus.PENDING.value,
                brand_registration_result["brand_registration_sid"]
            )

            logger.info(f"Successfully created brand registration for user: {user_id}")
            return brand_registration_result

        except TaraServiceError:
            raise
        except Exception as e:
            logger.error(f"Error creating brand registration for user {user_id}: {e}")
            raise TaraServiceError(f"Failed to create brand registration: {str(e)}")

    async def get_brand_status(self, user_id: str) -> Dict[str, Any]:
        """
        Get current brand registration status for user

        Args:
            user_id: Supabase auth user ID

        Returns:
            Dict containing brand status information

        Raises:
            TaraServiceError: If status retrieval fails
        """
        try:
            logger.debug(f"Getting brand status for user: {user_id}")

            # Get user's subaccount context
            subaccount_context = await self._get_user_subaccount_context(user_id)
            if not subaccount_context:
                raise TaraServiceError("User subaccount not found. Complete provisioning first.")

            subaccount_sid = subaccount_context["subaccount_sid"]

            # Get status from database
            status_info = await self.db_ops.get_business_profile_status(subaccount_sid)

            # If brand registration exists, get live status from Twilio
            if status_info["brand_registration_sid"]:
                twilio_status = await self.brand_registration_service.get_brand_status(
                    status_info["brand_registration_sid"]
                )
                status_info.update(twilio_status)

            logger.debug(f"Retrieved brand status for user: {user_id}")
            return status_info

        except TaraServiceError:
            raise
        except Exception as e:
            logger.error(f"Error getting brand status for user {user_id}: {e}")
            raise TaraServiceError(f"Failed to get brand status: {str(e)}")

    async def _get_existing_customer_profile(self, subaccount_sid: str) -> Optional[Dict[str, Any]]:
        """
        Get existing customer profile for subaccount to preserve Bundle SID

        Args:
            subaccount_sid: User's Twilio subaccount SID

        Returns:
            Existing customer profile data or None
        """
        try:
            response = await asyncio.to_thread(
                lambda: self.supabase.table("trust_hub_customer_profiles")
                .select("customer_profile_sid, bundle_sid, status")
                .eq("subaccount_sid", subaccount_sid)
                .eq("profile_type", "secondary")
                .limit(1)
                .execute()
            )

            if response.data:
                return response.data[0]
            return None

        except Exception as e:
            logger.warning(f"Could not retrieve existing customer profile for {subaccount_sid}: {e}")
            return None
