"""
Trust Hub Status Service for NeuroV CRM

This service provides comprehensive status tracking and notification management for
Twilio Trust Hub Customer Profiles. Handles status polling, database updates,
and multi-channel notifications.

Features:
- Customer Profile status polling from Twilio API
- Database updates with status changes and rejection reasons
- Multi-channel notifications (email, SMS, WhatsApp)
- Audit trail for status transitions
- Cost-aware notification management
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

from twilio.rest import Client
from twilio.base.exceptions import TwilioException

from app.core.config import settings
from app.db.supabase import get_supabase_admin
from app.services.notification_manager import NotificationManager, NotificationPriority
from app.services.notification_email_service import NotificationType

logger = logging.getLogger(__name__)


class CustomerProfileStatus(Enum):
    """Customer Profile status values from Twilio Trust Hub"""
    DRAFT = "draft"
    PENDING_REVIEW = "pending-review"
    IN_REVIEW = "in-review"
    TWILIO_APPROVED = "twilio-approved"
    TWILIO_REJECTED = "twilio-rejected"
    PROVISIONALLY_APPROVED = "provisionally-approved"


class TrustHubStatusService:
    """
    Service for managing Trust Hub Customer Profile status tracking and notifications
    """

    def __init__(self):
        """Initialize the Trust Hub status service"""
        # Initialize Twilio client
        if settings.TWILIO_ACCOUNT_SID and settings.TWILIO_AUTH_TOKEN:
            self.twilio_client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        else:
            self.twilio_client = None
            logger.warning("Twilio credentials not configured - status polling disabled")
        
        # Initialize Supabase admin client
        self.supabase = get_supabase_admin()
        
        # Initialize notification manager
        self.notification_manager = NotificationManager()
        
        # Polling configuration - significantly reduced frequency
        # Use environment variable to control polling, default to 1 hour instead of 5 minutes
        self.polling_interval = getattr(settings, 'TRUST_HUB_POLLING_INTERVAL', 3600)  # 1 hour default
        self.polling_enabled = getattr(settings, 'TRUST_HUB_POLLING_ENABLED', False)  # Disabled by default
        self.max_polling_age = timedelta(days=30)  # Stop polling after 30 days
        
        logger.info(f"TrustHubStatusService initialized - Polling: {'enabled' if self.polling_enabled else 'disabled'}, Interval: {self.polling_interval}s")

    async def update_customer_profile_status(
        self,
        customer_profile_sid: str,
        new_status: str,
        rejection_reasons: Optional[List[str]] = None,
        transition_source: str = "webhook"
    ) -> bool:
        """
        Update Customer Profile status in database and send notifications
        
        Args:
            customer_profile_sid: Twilio Customer Profile SID
            new_status: New status value
            rejection_reasons: List of rejection reasons (if rejected)
            transition_source: Source of status change (webhook, polling, manual)
            
        Returns:
            bool: True if update successful
        """
        try:
            # Find user by customer profile SID
            user_data = await self._find_user_by_customer_profile_sid(customer_profile_sid)
            if not user_data:
                logger.warning(f"No user found for customer profile SID: {customer_profile_sid}")
                return False
            
            # Update database records in subaccount_brands table
            await self._update_database_status(
                customer_profile_sid=customer_profile_sid,
                new_status=new_status,
                rejection_reasons=rejection_reasons
            )

            # Update detailed customer profile record with Bundle SID
            await self._update_customer_profile_details(
                customer_profile_sid=customer_profile_sid,
                new_status=new_status,
                rejection_reasons=rejection_reasons
            )
            
            # Log status transition
            await self._log_status_transition(
                subaccount_sid=user_data['subaccount_sid'],
                customer_profile_sid=customer_profile_sid,
                old_status=user_data.get('current_status'),
                new_status=new_status,
                transition_source=transition_source,
                rejection_reasons=rejection_reasons
            )
            
            # Send notifications based on status
            await self._send_status_notification(
                user_id=user_data['auth_user_id'],
                customer_profile_sid=customer_profile_sid,
                status=new_status,
                rejection_reasons=rejection_reasons
            )
            
            logger.info(f"Successfully updated customer profile {customer_profile_sid} status to {new_status}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update customer profile status: {e}")
            return False

    async def poll_pending_profiles(self) -> Dict[str, Any]:
        """
        Poll Twilio API for pending Customer Profile status updates
        
        Returns:
            Dict containing polling results and statistics
        """
        if not self.twilio_client:
            logger.warning("Twilio client not configured - skipping status polling")
            return {"success": False, "error": "Twilio client not configured"}
        
        try:
            # Get profiles that need status checking
            pending_profiles = await self._get_pending_profiles()
            
            results = {
                "success": True,
                "profiles_checked": 0,
                "status_updates": 0,
                "errors": []
            }
            
            for profile in pending_profiles:
                try:
                    # Check status via Twilio API
                    current_status = await self._check_profile_status_api(
                        profile['customer_profile_sid'],
                        profile['subaccount_sid']
                    )
                    
                    results["profiles_checked"] += 1
                    
                    # Update if status changed
                    if current_status and current_status != profile['status']:
                        await self.update_customer_profile_status(
                            customer_profile_sid=profile['customer_profile_sid'],
                            new_status=current_status,
                            transition_source="polling"
                        )
                        results["status_updates"] += 1
                        
                except Exception as e:
                    error_msg = f"Failed to check profile {profile['customer_profile_sid']}: {e}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)
            
            logger.info(f"Status polling completed: {results['profiles_checked']} checked, {results['status_updates']} updated")
            return results
            
        except Exception as e:
            logger.error(f"Status polling failed: {e}")
            return {"success": False, "error": str(e)}

    async def _find_user_by_customer_profile_sid(self, customer_profile_sid: str) -> Optional[Dict[str, Any]]:
        """Find user data by Customer Profile SID from subaccount_brands table"""
        try:
            # Get the profile data from trust_hub_customer_profiles table
            profile_result = await asyncio.to_thread(
                lambda: self.supabase.table("trust_hub_customer_profiles")
                .select("subaccount_sid, status")
                .eq("customer_profile_sid", customer_profile_sid)
                .execute()
            )

            if not profile_result.data:
                return None

            profile_data = profile_result.data[0]
            subaccount_sid = profile_data.get('subaccount_sid')

            # Then get the user mapping
            mapping_result = await asyncio.to_thread(
                lambda: self.supabase.table("user_subaccount_mapping")
                .select("auth_user_id, subaccount_sid")
                .eq("subaccount_sid", subaccount_sid)
                .execute()
            )

            if mapping_result.data and len(mapping_result.data) > 0:
                mapping_data = mapping_result.data[0]

                return {
                    'auth_user_id': mapping_data['auth_user_id'],
                    'subaccount_sid': mapping_data['subaccount_sid'],
                    'current_status': profile_data.get('status')
                }

            return None

        except Exception as e:
            logger.error(f"Failed to find user by customer profile SID: {e}")
            return None

    async def _update_database_status(
        self,
        customer_profile_sid: str,
        new_status: str,
        rejection_reasons: Optional[List[str]] = None
    ) -> None:
        """Update Customer Profile status in subaccount_brands table"""
        try:
            update_data = {
                "status": new_status,
                "date_updated": datetime.now(timezone.utc).isoformat()
            }

            # Add rejection reasons if provided (store as JSON string)
            if rejection_reasons:
                import json
                update_data["rejection_reasons"] = json.dumps(rejection_reasons)

            await asyncio.to_thread(
                lambda: self.supabase.table("trust_hub_customer_profiles")
                .update(update_data)
                .eq("customer_profile_sid", customer_profile_sid)
                .execute()
            )

            logger.info(f"Updated database status for profile {customer_profile_sid} in subaccount_brands")

        except Exception as e:
            logger.error(f"Failed to update database status: {e}")
            raise

    async def _update_customer_profile_details(
        self,
        customer_profile_sid: str,
        new_status: str,
        rejection_reasons: Optional[List[str]] = None
    ) -> None:
        """Update detailed Customer Profile record in subaccount_brands table"""
        try:
            update_data = {
                "business_profile_status": new_status,
                "date_updated": datetime.utcnow().isoformat()
            }

            # Add rejection reasons if provided (store as JSON string)
            if rejection_reasons:
                import json
                update_data["rejection_reasons"] = json.dumps(rejection_reasons)

            # Add status-specific timestamps
            if new_status == "twilio-approved":
                update_data["business_profile_approved_at"] = datetime.now(timezone.utc).isoformat()
            elif new_status == "twilio-rejected":
                update_data["business_profile_rejected_at"] = datetime.now(timezone.utc).isoformat()

            # Update subaccount_brands table
            response = await asyncio.to_thread(
                lambda: self.supabase.table("subaccount_brands")
                .update(update_data)
                .eq("customer_profile_sid", customer_profile_sid)
                .execute()
            )

            if response.data:
                logger.info(f"Updated customer profile details for {customer_profile_sid} in subaccount_brands")
            else:
                logger.warning(f"No customer profile record found for {customer_profile_sid} in subaccount_brands")

        except Exception as e:
            logger.error(f"Failed to update customer profile details: {e}")
            # Don't raise - this is supplementary to main status update

    async def _log_status_transition(
        self,
        subaccount_sid: str,
        customer_profile_sid: str,
        old_status: Optional[str],
        new_status: str,
        transition_source: str,
        rejection_reasons: Optional[List[str]] = None
    ) -> None:
        """Log status transition for audit trail"""
        try:
            # For now, just log to application logs since status_transitions table doesn't exist
            # This will be enhanced once the database migration is applied
            logger.info(f"Status Transition: {customer_profile_sid} from {old_status} to {new_status} via {transition_source}")
            if rejection_reasons:
                logger.info(f"Rejection reasons: {', '.join(rejection_reasons)}")

            # TODO: Store in status_transitions table once migration is applied
            # transition_data = {
            #     "subaccount_sid": subaccount_sid,
            #     "entity_type": "customer_profile",
            #     "entity_sid": customer_profile_sid,
            #     "old_status": old_status,
            #     "new_status": new_status,
            #     "transition_source": transition_source,
            #     "transition_details": {"rejection_reasons": rejection_reasons} if rejection_reasons else {},
            #     "created_at": datetime.now(timezone.utc).isoformat()
            # }

        except Exception as e:
            logger.warning(f"Failed to log status transition: {e}")
            # Don't raise - this is non-critical

    async def _send_status_notification(
        self,
        user_id: str,
        customer_profile_sid: str,
        status: str,
        rejection_reasons: Optional[List[str]] = None
    ) -> None:
        """Send appropriate notification based on status"""
        try:
            context_data = {
                'customer_profile_sid': customer_profile_sid,
                'status': status
            }
            
            if rejection_reasons:
                context_data['rejection_reasons'] = rejection_reasons
                context_data['rejection_reason'] = '; '.join(rejection_reasons)
            
            # Determine notification type and priority
            notification_type = None
            priority = NotificationPriority.NORMAL
            
            if status.lower() == 'pending-review':
                notification_type = NotificationType.CUSTOMER_PROFILE_SUBMITTED
                priority = NotificationPriority.NORMAL
            elif status.lower() == 'in-review':
                notification_type = NotificationType.CUSTOMER_PROFILE_IN_REVIEW
                priority = NotificationPriority.NORMAL
            elif status.lower() == 'twilio-approved':
                notification_type = NotificationType.CUSTOMER_PROFILE_APPROVED
                priority = NotificationPriority.HIGH
            elif status.lower() == 'twilio-rejected':
                notification_type = NotificationType.CUSTOMER_PROFILE_REJECTED
                priority = NotificationPriority.HIGH
            elif status.lower() == 'provisionally-approved':
                notification_type = NotificationType.CUSTOMER_PROFILE_PROVISIONALLY_APPROVED
                priority = NotificationPriority.HIGH
            
            if notification_type:
                await self.notification_manager.send_a2p_notification(
                    user_id=user_id,
                    notification_type=notification_type,
                    context_data=context_data,
                    priority=priority
                )
                
        except Exception as e:
            logger.error(f"Failed to send status notification: {e}")
            # Don't raise - this is non-critical

    async def _get_pending_profiles(self) -> List[Dict[str, Any]]:
        """Get Customer Profiles that need status checking from subaccount_brands table"""
        try:
            # Get profiles in pending or in-review status from subaccount_brands
            result = await asyncio.to_thread(
                lambda: self.supabase.table("trust_hub_customer_profiles")
                .select("customer_profile_sid, status, subaccount_sid, date_updated")
                .in_("status", ["pending-review", "in-review"])
                .not_.is_("customer_profile_sid", "null")
                .execute()
            )

            # Transform the data to match expected format
            profiles = []
            if result.data:
                for profile in result.data:
                    profiles.append({
                        "customer_profile_sid": profile["customer_profile_sid"],
                        "status": profile["status"],
                        "subaccount_sid": profile["subaccount_sid"],
                        "date_updated": profile["date_updated"]
                    })

            return profiles

        except Exception as e:
            logger.error(f"Failed to get pending profiles: {e}")
            return []

    async def _check_profile_status_api(self, customer_profile_sid: str, subaccount_sid: str) -> Optional[str]:
        """Check Customer Profile status via Twilio API"""
        try:
            # Use subaccount context for API call
            subaccount_client = self.twilio_client.api.accounts(subaccount_sid)

            # Fetch Customer Profile
            customer_profile = subaccount_client.trusthub.v1.customer_profiles(customer_profile_sid).fetch()

            return customer_profile.status

        except TwilioException as e:
            if e.status == 404:
                logger.warning(f"Customer Profile {customer_profile_sid} not found")
            else:
                logger.error(f"Twilio API error checking profile status: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to check profile status via API: {e}")
            return None

    async def start_status_polling(self) -> None:
        """Start background status polling service"""
        if not self.polling_enabled:
            logger.info("Trust Hub status polling is DISABLED - relying on webhooks and manual refresh")
            return

        logger.info(f"Starting Trust Hub status polling service (interval: {self.polling_interval}s)")

        while self.polling_enabled:
            try:
                await self.poll_pending_profiles()
                await asyncio.sleep(self.polling_interval)

            except Exception as e:
                logger.error(f"Status polling error: {e}")
                # Wait before retrying
                await asyncio.sleep(60)

    async def get_profile_status_summary(self, subaccount_sid: str) -> Dict[str, Any]:
        """Get status summary for all Customer Profiles in a subaccount from subaccount_brands table"""
        try:
            result = await asyncio.to_thread(
                lambda: self.supabase.table("trust_hub_customer_profiles")
                .select("customer_profile_sid, status, date_created, date_updated")
                .eq("subaccount_sid", subaccount_sid)
                .not_.is_("customer_profile_sid", "null")
                .order("date_created", desc=True)
                .execute()
            )

            profiles = []
            if result.data:
                for profile in result.data:
                    profiles.append({
                        "customer_profile_sid": profile["customer_profile_sid"],
                        "status": profile["status"],
                        "date_created": profile["date_created"],
                        "date_updated": profile["date_updated"]
                    })

            # Calculate status counts
            status_counts = {}
            for profile in profiles:
                status = profile['status']
                status_counts[status] = status_counts.get(status, 0) + 1

            return {
                "total_profiles": len(profiles),
                "status_counts": status_counts,
                "profiles": profiles
            }

        except Exception as e:
            logger.error(f"Failed to get profile status summary: {e}")
            return {"total_profiles": 0, "status_counts": {}, "profiles": []}

    async def retry_rejected_profile(self, customer_profile_sid: str) -> bool:
        """Mark a rejected profile for resubmission"""
        try:
            # Update status to draft for resubmission
            await self._update_database_status(
                customer_profile_sid=customer_profile_sid,
                new_status="draft"
            )

            logger.info(f"Marked profile {customer_profile_sid} for resubmission")
            return True

        except Exception as e:
            logger.error(f"Failed to retry rejected profile: {e}")
            return False


# Global instance for background polling
trust_hub_status_service = TrustHubStatusService()
