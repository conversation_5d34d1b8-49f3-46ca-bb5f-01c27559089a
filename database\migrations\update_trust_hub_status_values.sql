-- Migration: Update Trust Hub Customer Profiles Status Values
-- Date: 2025-01-15
-- Purpose: Add support for all Twilio Trust Hub API status values

-- Drop the existing status constraint
ALTER TABLE public.trust_hub_customer_profiles 
DROP CONSTRAINT IF EXISTS chk_status_valid_values;

-- Add updated status constraint with all Twilio API status values
ALTER TABLE public.trust_hub_customer_profiles 
ADD CONSTRAINT chk_status_valid_values CHECK (
  (status IS NULL)
  OR (
    (status)::text = ANY (
      ARRAY[
        ('draft'::character varying)::text,
        ('pending-review'::character varying)::text,
        ('in-review'::character varying)::text,
        ('twilio-approved'::character varying)::text,
        ('twilio-rejected'::character varying)::text,
        -- Keep legacy status values for backward compatibility
        ('submitted'::character varying)::text,
        ('approved'::character varying)::text,
        ('rejected'::character varying)::text
      ]
    )
  )
);

-- Add index for efficient status-based queries
CREATE INDEX IF NOT EXISTS idx_trust_hub_customer_profiles_status_updated 
ON public.trust_hub_customer_profiles USING btree (status, updated_at) 
TABLESPACE pg_default;

-- Add column for tracking last status check timestamp
ALTER TABLE public.trust_hub_customer_profiles 
ADD COLUMN IF NOT EXISTS last_status_check timestamp with time zone NULL;

-- Add index for last status check
CREATE INDEX IF NOT EXISTS idx_trust_hub_customer_profiles_last_status_check 
ON public.trust_hub_customer_profiles USING btree (last_status_check) 
TABLESPACE pg_default;

-- Update existing records to use new status values (optional migration)
-- Uncomment if you want to migrate existing data
/*
UPDATE public.trust_hub_customer_profiles 
SET status = CASE 
  WHEN status = 'approved' THEN 'twilio-approved'
  WHEN status = 'rejected' THEN 'twilio-rejected'
  WHEN status = 'submitted' THEN 'pending-review'
  ELSE status
END
WHERE status IN ('approved', 'rejected', 'submitted');
*/

-- Verify the constraint is working
-- This should succeed
INSERT INTO public.trust_hub_customer_profiles (subaccount_sid, status) 
VALUES ('TEST_CONSTRAINT_CHECK', 'twilio-approved') 
ON CONFLICT (subaccount_sid) DO NOTHING;

-- Clean up test record
DELETE FROM public.trust_hub_customer_profiles 
WHERE subaccount_sid = 'TEST_CONSTRAINT_CHECK';

-- Add comment to document the status values
COMMENT ON COLUMN public.trust_hub_customer_profiles.status IS 
'Trust Hub Customer Profile status. Valid values: draft, pending-review, in-review, twilio-approved, twilio-rejected, submitted (legacy), approved (legacy), rejected (legacy)';

COMMENT ON COLUMN public.trust_hub_customer_profiles.last_status_check IS 
'Timestamp of the last manual status refresh check via Twilio API';
