"use client"

import React, { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Building2,
  Shield,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  ArrowRight,
  RefreshCw,
  Info,
  FileEdit,
  Tag
} from "lucide-react"
import { SecondaryCustomerProfileStatus } from "./secondary-customer-profile-status"
import {
  useSecondaryCustomerProfileStatus,
  useCanCreateSecondaryCustomerProfile,
  useCreateSecondaryCustomerProfile
} from "@/hooks/use-secondary-customer-profile"
import { useBusinessProfileEnhanced } from "@/hooks/use-business-profile-enhanced"
import { useRefreshTrustHubCustomerProfileStatus } from "@/hooks/use-business-profile"

interface SecondaryCustomerProfileSectionProps {
  className?: string
  onProfileCreated?: (profileData: any) => void
  onStatusChange?: (status: string) => void
  isBusinessProfileComplete?: boolean
  enabled?: boolean // Control when to fetch data
  onOpenBusinessProfile?: () => void // Callback to open business profile modal
  onOpenA2pBrand?: () => void // Callback to open A2P Brand modal
}

export function SecondaryCustomerProfileSection({
  className = "",
  onProfileCreated,
  onStatusChange,
  isBusinessProfileComplete = false,
  enabled = true,
  onOpenBusinessProfile,
  onOpenA2pBrand
}: SecondaryCustomerProfileSectionProps) {
  const [showFullStatus, setShowFullStatus] = useState(false)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Hooks for secondary customer profile operations - only fetch when enabled
  const { data: statusData, isLoading } = useSecondaryCustomerProfileStatus(enabled)
  const { canCreate, hasProfile } = useCanCreateSecondaryCustomerProfile(enabled)
  const createMutation = useCreateSecondaryCustomerProfile()

  // Fetch business profile status to determine display status
  const { completionStatus, trustHubStatus, refetchTrustHubStatus } = useBusinessProfileEnhanced(enabled)

  // Hook for manual Trust Hub status refresh
  const refreshTrustHubMutation = useRefreshTrustHubCustomerProfileStatus()

  const status = statusData?.data?.status
  const profileSid = statusData?.data?.customer_profile_sid

  // Use Trust Hub status as primary source of truth
  const trustHubProfileStatus = trustHubStatus?.status
  const displayStatus = trustHubProfileStatus || completionStatus?.displayStatus || status

  // Check if business profile is completed (submitted to Twilio and approved)
  const isBusinessProfileApproved = trustHubProfileStatus === 'twilio-approved'
  const isBusinessProfileSubmitted = trustHubProfileStatus && ['pending-review', 'in-review', 'twilio-approved'].includes(trustHubProfileStatus)
  const shouldShowA2pBrandSection = isBusinessProfileApproved

  // Handle profile creation success
  React.useEffect(() => {
    if (createMutation.isSuccess && createMutation.data && onProfileCreated) {
      onProfileCreated(createMutation.data.data)
    }
  }, [createMutation.isSuccess, createMutation.data, onProfileCreated])

  // Set up polling for pending states
  useEffect(() => {
    const isPendingState = trustHubProfileStatus && ['pending-review', 'in-review'].includes(trustHubProfileStatus)

    if (enabled && isPendingState) {
      // Start polling every 2 minutes for pending states (reduced from 30 seconds)
      pollingIntervalRef.current = setInterval(() => {
        console.log('🔄 Polling Trust Hub status for pending profile...')
        refetchTrustHubStatus()
      }, 120000) // 2 minutes (reduced from 30 seconds to minimize backend load)

      console.log('✅ Started polling for Trust Hub status updates (2-minute interval)')
    } else {
      // Clear polling when not in pending state or disabled
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
        console.log('⏹️ Stopped polling for Trust Hub status updates')
      }
    }

    // Cleanup on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }
  }, [enabled, trustHubProfileStatus, refetchTrustHubStatus])

  // Notify parent of status changes and handle UI flow
  useEffect(() => {
    if (trustHubProfileStatus && onStatusChange) {
      onStatusChange(trustHubProfileStatus)
    }

    // Handle status-based UI flow
    if (trustHubProfileStatus === 'twilio-approved') {
      console.log('🎉 Trust Hub profile approved - unlocking A2P brand registration')
      // The component will automatically hide itself when approved (see render logic below)
      // Parent component should show A2P brand configuration component
    } else if (trustHubProfileStatus === 'twilio-rejected') {
      console.log('❌ Trust Hub profile rejected - showing error state')
      // Component will show rejection reason and allow retry
    }
  }, [trustHubProfileStatus, onStatusChange])

  // Get status display information
  const getStatusInfo = (status?: string) => {
    switch (status) {
      case 'twilio-approved':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Approved',
          description: 'Your business profile has been approved by Twilio',
          canProceed: true
        }
      case 'pending-review':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: 'Pending Review',
          description: "You've successfully submitted your business profile details. It may take 24-48 hours for Twilio to review and approve. We'll send you an SMS notification once the status changes.",
          canProceed: false
        }
      case 'in-review':
        return {
          icon: Clock,
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          title: 'Under Review',
          description: "You've successfully submitted your business profile details. It may take 24-48 hours for Twilio to review and approve. We'll send you an SMS notification once the status changes.",
          canProceed: false
        }
      case 'provisionally-approved':
        return {
          icon: Shield,
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          title: 'Provisionally Approved',
          description: 'Profile provisionally approved, final review pending.',
          canProceed: true
        }
      case 'twilio-rejected':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Rejected',
          description: trustHubStatus?.rejection_reason
            ? `Profile was rejected: ${trustHubStatus.rejection_reason}. Please update your business profile and resubmit.`
            : 'Profile was rejected. Please review your business profile information, make necessary corrections, and resubmit.',
          canProceed: true // Allow user to retry by opening business profile
        }
      case 'expired':
        return {
          icon: AlertCircle,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Expired',
          description: 'Profile has expired. Please create a new one.',
          canProceed: false
        }
      case 'drafted':
      case 'draft':
        return {
          icon: FileEdit,
          color: 'text-yellow-700',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-300',
          title: 'Draft',
          description: 'Business profile has been saved but not submitted yet.',
          canProceed: true
        }
      case 'not_created':
      case 'not_found':
        return {
          icon: Building2,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Not Created',
          description: 'Business profile not created yet.',
          canProceed: true
        }
      default:
        return {
          icon: Building2,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Not Created',
          description: 'Secondary customer profile not created yet.',
          canProceed: true
        }
    }
  }

  const statusInfo = getStatusInfo(displayStatus)
  const StatusIcon = statusInfo.icon

  const handleCreateProfile = () => {
    // Open the business profile modal instead of creating customer profile directly
    if (onOpenBusinessProfile) {
      onOpenBusinessProfile()
    } else {
      console.warn('onOpenBusinessProfile callback not provided')
    }
  }

  // Hide component when Trust Hub profile is approved
  if (trustHubProfileStatus === 'twilio-approved') {
    return (
      <div className={`space-y-4 ${className}`}>
        {/* Approved Status - Minimal Display */}
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium text-green-900">Business Profile Approved</h3>
                <p className="text-sm text-green-700">Your Trust Hub customer profile has been approved. You can now proceed to A2P brand registration.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Status Card */}
      <Card className={`${statusInfo.borderColor} transition-all duration-200`}>
        <CardHeader className={`${statusInfo.bgColor} border-b ${statusInfo.borderColor}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 ${statusInfo.bgColor} rounded-full flex items-center justify-center border ${statusInfo.borderColor}`}>
                <StatusIcon className={`w-5 h-5 ${statusInfo.color}`} />
              </div>
              <div>
                <CardTitle className="text-lg">
                  Secondary Customer Profile
                </CardTitle>
                <CardDescription>
                  Compliance profile for your Twilio subaccount
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {status && (
                <Badge
                  variant={status === 'twilio-approved' ? 'default' : 'secondary'}
                  className="capitalize"
                >
                  {statusInfo.title}
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => refreshTrustHubMutation.mutate()}
                disabled={refreshTrustHubMutation.isPending}
                className="h-8 w-8 p-0"
                title="Refresh status from Twilio API"
              >
                <RefreshCw className={`h-4 w-4 ${refreshTrustHubMutation.isPending ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Status Description */}
            <p className="text-gray-600">
              {statusInfo.description}
            </p>

            {/* Prerequisites Check */}
            {!isBusinessProfileComplete && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Complete your business profile first before creating a secondary customer profile.
                </AlertDescription>
              </Alert>
            )}

            {/* Profile Information */}
            {hasProfile && profileSid && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Profile ID:</span>
                    <p className="text-gray-600 font-mono text-xs mt-1">
                      {profileSid}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Type:</span>
                    <p className="text-gray-600 mt-1">Secondary (Subaccount)</p>
                  </div>
                  {statusData?.data?.date_created && (
                    <div>
                      <span className="font-medium text-gray-700">Created:</span>
                      <p className="text-gray-600 mt-1">
                        {new Date(statusData.data.date_created).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                  {statusData?.data?.valid_until && (
                    <div>
                      <span className="font-medium text-gray-700">Valid Until:</span>
                      <p className="text-gray-600 mt-1">
                        {new Date(statusData.data.valid_until).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Error Display */}
            {status === 'twilio-rejected' && statusData?.data?.errors && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Rejection Details:</strong>
                  <pre className="mt-2 text-xs whitespace-pre-wrap">
                    {JSON.stringify(statusData.data.errors, null, 2)}
                  </pre>
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              {/* Status-based Action Button */}
              {(() => {
                switch (trustHubProfileStatus) {
                  case 'not_created':
                  case 'not_found':
                  case undefined:
                  case null:
                    return (
                      <Button
                        onClick={handleCreateProfile}
                        className="flex-1"
                      >
                        <Building2 className="w-4 h-4 mr-2" />
                        Create Secondary Customer Profile
                      </Button>
                    )

                  case 'draft':
                    return (
                      <Button
                        onClick={handleCreateProfile}
                        className="flex-1"
                      >
                        <FileEdit className="w-4 h-4 mr-2" />
                        Complete Business Profile
                      </Button>
                    )

                  case 'pending-review':
                  case 'in-review':
                    // No action button for pending states - just informational text
                    return null

                  case 'twilio-approved':
                    // No action button needed - profile is approved
                    return null

                  case 'twilio-rejected':
                    return (
                      <Button
                        onClick={handleCreateProfile}
                        variant="destructive"
                        className="flex-1"
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Resubmit Business Profile
                      </Button>
                    )

                  default:
                    return (
                      <Button
                        onClick={handleCreateProfile}
                        className="flex-1"
                      >
                        <Building2 className="w-4 h-4 mr-2" />
                        Create Secondary Customer Profile
                      </Button>
                    )
                }
              })()}

              {/* View Details Button - only when profile exists */}
              {hasProfile && (
                <Button
                  variant="outline"
                  onClick={() => setShowFullStatus(!showFullStatus)}
                  className="flex-1 sm:flex-none"
                >
                  {showFullStatus ? 'Hide Details' : 'View Details'}
                  <ArrowRight className={`w-4 h-4 ml-2 transition-transform ${showFullStatus ? 'rotate-90' : ''}`} />
                </Button>
              )}
            </div>

            {/* Next Steps Preview */}
            {trustHubProfileStatus && !showFullStatus && (
              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">What's Next?</h4>
                <div className="space-y-1">
                  {trustHubProfileStatus === 'twilio-approved' && (
                    <p className="text-sm text-blue-800">
                      ✅ Your secondary customer profile is approved! You can now proceed with A2P brand registration.
                    </p>
                  )}
                  {(trustHubProfileStatus === 'pending-review' || trustHubProfileStatus === 'in-review') && (
                    <p className="text-sm text-blue-800">
                      ⏳ Your profile is under review. This typically takes 24-48 hours. We'll send you an SMS notification once the status changes.
                    </p>
                  )}
                  {trustHubProfileStatus === 'provisionally-approved' && (
                    <p className="text-sm text-blue-800">
                      🛡️ Your profile is provisionally approved. You can begin preparing for brand registration.
                    </p>
                  )}
                  {(trustHubProfileStatus === 'twilio-rejected' || trustHubProfileStatus === 'expired') && (
                    <p className="text-sm text-blue-800">
                      🔄 Please review the rejection details and resubmit your business profile with corrections.
                    </p>
                  )}
                  {trustHubProfileStatus === 'draft' && (
                    <p className="text-sm text-blue-800">
                      📝 Complete your business profile and submit it for Twilio review.
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Status Component */}
      {showFullStatus && hasProfile && (
        <SecondaryCustomerProfileStatus
          enabled={enabled}
          onStatusChange={onStatusChange}
          className="border-t-0 rounded-t-none"
        />
      )}
    </div>
  )
}
