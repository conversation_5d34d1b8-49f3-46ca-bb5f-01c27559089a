"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import { useToast } from '@/hooks/use-toast'
import {
  getBusinessProfile,
  saveBusinessProfile,
  autoSaveBusinessProfile,
  getBusinessProfileStatus,
  getSecondaryCustomerProfileStatus,
  getTrustHubCustomerProfileStatus,
  refreshTrustHubCustomerProfileStatus,
  type BusinessProfileRequest,
  type BusinessProfileResponse,
  type AutoSaveBusinessProfileRequest,
  type AutoSaveResponse,
  type BusinessProfileStatusResponse,
  type SecondaryCustomerProfileStatusResponse
} from '@/lib/api/tara'

// Query keys for React Query
export const BUSINESS_PROFILE_QUERY_KEYS = {
  profile: ['business-profile'] as const,
  status: ['business-profile', 'status'] as const,
  secondaryStatus: ['business-profile', 'secondary-status'] as const,
  trustHubStatus: ['business-profile', 'trust-hub-status'] as const,
} as const

/**
 * Hook to fetch business profile data
 */
export function useBusinessProfile(enabled: boolean = true) {
  console.log('🔄 [HOOK] useBusinessProfile called with enabled:', enabled);

  const query = useQuery({
    queryKey: BUSINESS_PROFILE_QUERY_KEYS.profile,
    queryFn: () => {
      console.log('🔄 [HOOK] React Query executing getBusinessProfile...');
      return getBusinessProfile();
    },
    enabled, // Only fetch when enabled
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error?.message?.includes('Authentication') || error?.message?.includes('401')) {
        return false
      }
      return failureCount < 2
    },
    refetchOnWindowFocus: false,
  })

  // Log query state changes
  console.log('🔄 [HOOK] Business profile query state:', {
    enabled,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isError: query.isError,
    error: query.error,
    hasData: !!query.data
  });

  return query;
}

/**
 * Hook to save business profile data
 */
export function useSaveBusinessProfile() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: saveBusinessProfile,
    onSuccess: (data) => {
      // Invalidate and refetch business profile data
      queryClient.invalidateQueries({ queryKey: BUSINESS_PROFILE_QUERY_KEYS.profile })
      
      toast({
        title: "Business Profile Saved",
        description: "Your business profile has been saved successfully.",
        variant: "default",
      })
    },
    onError: (error: any) => {
      console.error('Failed to save business profile:', error)
      
      toast({
        title: "Save Failed",
        description: error?.message || "Failed to save business profile. Please try again.",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to auto-save business profile data (debounced)
 */
export function useAutoSaveBusinessProfile() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: autoSaveBusinessProfile,
    onSuccess: (data) => {
      // Only update cached data if auto-save was actually successful
      if (data.success && data.data) {
        queryClient.setQueryData(
          BUSINESS_PROFILE_QUERY_KEYS.profile,
          (oldData: BusinessProfileResponse | undefined) => {
            if (!oldData) return oldData

            return {
              ...oldData,
              data: {
                ...oldData.data,
                // Update completion percentage and last saved time
                completion_percentage: data.data.completion_percentage,
                updated_at: data.auto_saved_at,
              }
            }
          }
        )
      } else {
        // Auto-save was skipped or failed, just log it
        console.log('Auto-save skipped:', data.message)
      }
    },
    onError: (error: any) => {
      console.error('Auto-save failed:', error)
      // Don't show toast for auto-save failures to avoid spam
    },
  })
}

/**
 * Hook to manage business profile form state with auto-save
 */
export function useBusinessProfileForm(enabled: boolean = true) {
  const { data: profileData, isLoading, error } = useBusinessProfile(enabled)
  const saveMutation = useSaveBusinessProfile()
  const autoSaveMutation = useAutoSaveBusinessProfile()

  // Auto-save function with debouncing
  const autoSave = useMutation({
    mutationFn: async (partialData: AutoSaveBusinessProfileRequest) => {
      // Debounce auto-save calls
      await new Promise(resolve => setTimeout(resolve, 1000))
      return autoSaveMutation.mutateAsync(partialData)
    },
    onError: (error) => {
      console.error('Auto-save error:', error)
    }
  })

  const handleSave = async (formData: BusinessProfileRequest) => {
    try {
      await saveMutation.mutateAsync(formData)
      return true
    } catch (error) {
      console.error('Save error:', error)
      return false
    }
  }

  const handleAutoSave = async (partialData: AutoSaveBusinessProfileRequest) => {
    try {
      await autoSave.mutateAsync(partialData)
    } catch (error) {
      console.error('Auto-save error:', error)
    }
  }

  return {
    // Data
    profileData: profileData?.data,
    isLoading,
    error,
    
    // Mutations
    save: handleSave,
    autoSave: handleAutoSave,
    
    // Status
    isSaving: saveMutation.isPending,
    isAutoSaving: autoSave.isPending,
    saveError: saveMutation.error,
    
    // Completion percentage
    completionPercentage: profileData?.data?.completion_percentage || 0,
  }
}

/**
 * Hook to fetch business profile status
 */
export function useBusinessProfileStatus(enabled: boolean = true) {
  return useQuery({
    queryKey: BUSINESS_PROFILE_QUERY_KEYS.status,
    queryFn: getBusinessProfileStatus,
    enabled, // Only fetch when enabled
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: true,
  })
}

/**
 * Hook to fetch secondary customer profile status
 */
export function useSecondaryCustomerProfileStatus(enabled: boolean = true) {
  return useQuery({
    queryKey: BUSINESS_PROFILE_QUERY_KEYS.secondaryStatus,
    queryFn: getSecondaryCustomerProfileStatus,
    enabled, // Only fetch when enabled
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    retry: 2,
    refetchOnWindowFocus: true,
  })
}

/**
 * Hook to fetch Trust Hub customer profile status from database
 */
export function useTrustHubCustomerProfileStatus(enabled: boolean = true) {
  return useQuery({
    queryKey: BUSINESS_PROFILE_QUERY_KEYS.trustHubStatus,
    queryFn: getTrustHubCustomerProfileStatus,
    enabled, // Only fetch when enabled
    staleTime: 5 * 60 * 1000, // 5 minutes - increased from 30 seconds
    gcTime: 10 * 60 * 1000, // 10 minutes - increased from 2 minutes
    retry: 2,
    refetchOnWindowFocus: false, // Disabled to prevent excessive API calls
    refetchInterval: false, // Disabled automatic refetch - rely on manual refresh and webhooks
  })
}

/**
 * Hook to refresh Trust Hub customer profile status from Twilio API
 */
export function useRefreshTrustHubCustomerProfileStatus() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: refreshTrustHubCustomerProfileStatus,
    onSuccess: (data) => {
      // Invalidate and refetch Trust Hub status
      queryClient.invalidateQueries({ queryKey: BUSINESS_PROFILE_QUERY_KEYS.trustHubStatus })

      // Show success toast with status change information
      if (data.data.status_changed) {
        toast({
          title: "Status Updated!",
          description: `Trust Hub status changed from "${data.data.previous_status}" to "${data.data.status}"`,
          variant: "default",
        })
      } else {
        toast({
          title: "Status Refreshed",
          description: `Trust Hub status is "${data.data.status}" (no change)`,
          variant: "default",
        })
      }
    },
    onError: (error: any) => {
      console.error('Trust Hub status refresh failed:', error)
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh Trust Hub status. Please try again.",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to get comprehensive business profile status and unlock information
 */
export function useBusinessProfileStatusInfo(enabled: boolean = true) {
  const { data: profileData, error: profileError, isLoading: profileLoading } = useBusinessProfile(enabled)
  const { data: statusData, isLoading: statusLoading, refetch: refetchStatus } = useBusinessProfileStatus(enabled)
  const { data: secondaryStatusData, isLoading: secondaryStatusLoading, refetch: refetchSecondaryStatus } = useSecondaryCustomerProfileStatus(enabled)



  // Memoize unlock status to prevent infinite re-renders
  const unlockStatus = useMemo(() => {
    // Handle loading state
    if (!profileData) {
      return {
        isUnlocked: false,
        canSubmit: false,
        nextStep: 'Loading business profile...',
        completionPercentage: 0
      }
    }

    // Handle API error or no data
    if (!profileData.data) {
      return {
        isUnlocked: false,
        canSubmit: false,
        nextStep: 'Complete business profile information',
        completionPercentage: 0
      }
    }

    const profile = profileData.data
    // Calculate completion percentage since it's not in the API response
    const completionPercentage = calculateCompletionPercentage(profile)
    const isComplete = completionPercentage >= 100
    const isSubmitted = profile.status !== 'draft'

    return {
      isUnlocked: true,
      canSubmit: isComplete && !isSubmitted,
      nextStep: isComplete
        ? (isSubmitted ? 'Waiting for approval' : 'Submit for review')
        : 'Complete remaining fields',
      completionPercentage,
      status: profile.status,
      brandRegistrationStatus: profile.brand_registration_status
    }
  }, [profileData])

  const refreshAllStatus = useCallback(async () => {
    await Promise.all([
      refetchStatus(),
      refetchSecondaryStatus()
    ])
  }, [refetchStatus, refetchSecondaryStatus])

  return {
    ...unlockStatus,
    profileData: profileData?.data,
    statusData: statusData?.data,
    secondaryStatusData: secondaryStatusData?.data,
    isLoadingStatus: statusLoading || secondaryStatusLoading,
    refreshStatus: refreshAllStatus,
    customerProfileSid: profileData?.data?.customer_profile_sid || secondaryStatusData?.data?.customer_profile_sid,
    brandRegistrationSid: profileData?.data?.brand_registration_sid,
  }
}

/**
 * Utility function to calculate completion percentage
 */
export function calculateCompletionPercentage(profile: Partial<BusinessProfileRequest> | null | undefined): number {
  if (!profile) {
    return 0
  }

  const requiredFields = [
    'company_name',
    'business_type',
    'business_registration_identifier',
    'business_registration_number',
    'website',
    'vertical',
    'business_regions',
    'address.street',
    'address.city',
    'address.state',
    'address.postal_code',
    'address.country',
    'representative.first_name',
    'representative.last_name',
    'representative.email',
    'representative.phone',
    'representative.job_title'
  ]

  const completedFields = requiredFields.filter(field => {
    const value = field.split('.').reduce((obj: any, key) => obj?.[key], profile)
    return value && value !== '' && (Array.isArray(value) ? value.length > 0 : true)
  })

  return Math.round((completedFields.length / requiredFields.length) * 100)
}
