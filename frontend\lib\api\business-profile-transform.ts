/**
 * Data transformation utilities for Business Profile
 * Maps between frontend form data and backend API schemas
 */

import type { BusinessProfileFormData } from "@/components/business-profile-form"
import type {
  BusinessProfileRequest,
  BusinessProfileResponse,
  AutoSaveBusinessProfileRequest
} from "@/lib/api/tara"
import {
  getCountryCodeByName,
  getRegionCodeByName,
  getCountryByCode,
  getRegistrationTypeByName,
  BUSINESS_REGIONS,
  REGISTRATION_ID_TYPES
} from "@/lib/constants/countries"

/**
 * Map frontend business type to backend enum
 */
function mapBusinessType(frontendType: string): BusinessProfileRequest['business_type'] {
  const typeMap: Record<string, BusinessProfileRequest['business_type']> = {
    'Corporation': 'corporation',
    'LLC': 'llc',
    'Partnership': 'partnership',
    'Sole Proprietorship': 'sole_proprietorship'
  }
  return typeMap[frontendType] || 'other'
}

/**
 * Map frontend region names to backend region codes
 */
function mapRegionsToBackend(frontendRegions: string[]): string[] {
  return frontendRegions.map(region => {
    const regionCode = getRegionCodeByName(region);
    return regionCode || region; // Fallback to original if no mapping found
  });
}

/**
 * Transform frontend form data to backend API format
 * Returns both the transformed data and any validation errors
 */
export function transformFormDataToApiRequest(formData: BusinessProfileFormData): BusinessProfileRequest {
  // Map country name to ISO code - handle gracefully without throwing
  const countryCode = getCountryCodeByName(formData.country);
  if (!countryCode) {
    // For now, use a default or empty value instead of throwing
    // The backend validation will catch this and provide proper feedback
    console.warn(`Invalid country selected: ${formData.country}. Backend will handle validation.`);
  }

  // Map regions to backend format
  const mappedRegions = mapRegionsToBackend(formData.regions);

  // Map registration ID type to backend format
  const registrationType = getRegistrationTypeByName(formData.registrationIdType);
  const backendRegistrationIdType = registrationType?.backendCode || formData.registrationIdType;

  return {
    company_name: formData.legalBusinessName,
    business_type: mapBusinessType(formData.businessType),
    business_registration_id_type: backendRegistrationIdType,
    business_registration_number: formData.registrationId,
    business_identity: 'direct_customer', // Default for secondary customer profiles
    website: formData.website,
    vertical: formData.industry,
    business_regions: mappedRegions,
    business_regions_of_operation: mappedRegions.join(', '), // Convert array to string for Twilio
    social_media_profile_urls: formData.socialMediaUrls || undefined,
    address: {
      street: formData.streetAddress + (formData.streetSecondary ? ` ${formData.streetSecondary}` : ''),
      city: formData.city,
      state: formData.stateProvince,
      postal_code: formData.postalCode,
      country: countryCode || formData.country || '' // Use ISO code, fallback to original, or empty string
    },
    representative: {
      first_name: formData.repFirstName,
      last_name: formData.repLastName,
      email: formData.repEmail,
      phone: formData.repPhone,
      job_title: formData.repJobTitle,
      business_title: formData.repBusinessTitle
    }
  }
}

/**
 * Map backend business type to frontend display format
 */
function mapBackendBusinessType(backendType: string): string {
  const typeMap: Record<string, string> = {
    'corporation': 'Corporation',
    'llc': 'LLC',
    'partnership': 'Partnership',
    'sole_proprietorship': 'Sole Proprietorship',
    'non_profit': 'Non Profit',
    'government': 'Government',
    'other': 'Other'
  }
  return typeMap[backendType] || 'Other'
}

/**
 * Map backend region codes to frontend region names
 */
function mapRegionsToFrontend(backendRegions: string[]): string[] {
  return backendRegions.map(regionCode => {
    const region = BUSINESS_REGIONS.find(r => r.code === regionCode);
    return region ? region.name : regionCode; // Fallback to original if no mapping found
  });
}

/**
 * Check if a value is a backend placeholder that should be treated as empty
 */
function isPlaceholderValue(value: string | undefined | null): boolean {
  if (!value) return true;
  const placeholders = [
    "[Not provided]",
    "<EMAIL>",
    "0000000000",
    "00000",
    "XX"
  ];
  return placeholders.includes(value);
}

/**
 * Clean a value by returning empty string if it's a placeholder
 */
function cleanPlaceholderValue(value: string | undefined | null): string {
  if (!value || isPlaceholderValue(value)) return "";
  return value;
}

/**
 * Transform backend API response to frontend form data
 */
export function transformApiResponseToFormData(apiData: BusinessProfileResponse['data']): Partial<BusinessProfileFormData> {
  if (!apiData) return {}

  // Map country ISO code back to full name (only if not a placeholder)
  const countryCode = apiData.address?.country;
  let countryName = "";
  if (countryCode && !isPlaceholderValue(countryCode)) {
    const country = getCountryByCode(countryCode);
    countryName = country ? country.name : countryCode;
  }

  // Map regions back to frontend format (filter out placeholders)
  const mappedRegions = apiData.business_regions ?
    mapRegionsToFrontend(apiData.business_regions.filter(region => !isPlaceholderValue(region))) : [];

  // Map registration ID type from backend constraint value to frontend display format
  let registrationIdType = "";
  if (apiData.business_registration_id_type && !isPlaceholderValue(apiData.business_registration_id_type)) {
    // Find the frontend display name that maps to this backend constraint value
    const registrationType = REGISTRATION_ID_TYPES.find(
      type => type.backendCode === apiData.business_registration_id_type
    );
    registrationIdType = registrationType ? registrationType.name : apiData.business_registration_id_type;
  }

  return {
    legalBusinessName: cleanPlaceholderValue(apiData.company_name),
    businessType: apiData.business_type && !isPlaceholderValue(apiData.business_type) ?
      mapBackendBusinessType(apiData.business_type) : "",
    registrationIdType: registrationIdType,
    registrationId: cleanPlaceholderValue(apiData.business_registration_number || apiData.ein), // Support both new and legacy fields
    industry: cleanPlaceholderValue(apiData.vertical),
    email: cleanPlaceholderValue(apiData.representative?.email),
    website: cleanPlaceholderValue(apiData.website),
    socialMediaUrls: cleanPlaceholderValue(apiData.social_media_profile_urls) || "",
    regions: mappedRegions,
    country: countryName, // Convert ISO code back to full name
    stateProvince: cleanPlaceholderValue(apiData.address?.state),
    streetAddress: cleanPlaceholderValue(apiData.address?.street),
    streetSecondary: "", // Combined in backend street field
    city: cleanPlaceholderValue(apiData.address?.city),
    postalCode: cleanPlaceholderValue(apiData.address?.postal_code),
    repFirstName: cleanPlaceholderValue(apiData.representative?.first_name),
    repLastName: cleanPlaceholderValue(apiData.representative?.last_name),
    repEmail: cleanPlaceholderValue(apiData.representative?.email),
    repPhone: cleanPlaceholderValue(apiData.representative?.phone),
    repJobTitle: cleanPlaceholderValue(apiData.representative?.job_title),
    repBusinessTitle: cleanPlaceholderValue(apiData.representative?.business_title)
  }
}

/**
 * Validate form data before transformation
 */
export function validateFormData(formData: BusinessProfileFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Required field validation
  if (!formData.legalBusinessName.trim()) errors.push("Legal business name is required")
  if (!formData.businessType) errors.push("Business type is required")
  if (!formData.registrationId.trim()) errors.push("Registration ID is required")
  if (!formData.industry) errors.push("Industry is required")
  if (!formData.email.trim()) errors.push("Business email is required")
  if (!formData.website.trim()) errors.push("Website is required")
  if (formData.regions.length === 0) errors.push("At least one region is required")
  if (!formData.country) errors.push("Country is required")
  if (!formData.stateProvince) errors.push("State/Province is required")
  if (!formData.streetAddress.trim()) errors.push("Street address is required")
  if (!formData.city.trim()) errors.push("City is required")
  if (!formData.postalCode.trim()) errors.push("Postal code is required")
  if (!formData.repFirstName.trim()) errors.push("Representative first name is required")
  if (!formData.repLastName.trim()) errors.push("Representative last name is required")
  if (!formData.repEmail.trim()) errors.push("Representative email is required")
  if (!formData.repPhone.trim()) errors.push("Representative phone is required")
  if (!formData.repJobTitle.trim()) errors.push("Representative job title is required")

  // Format validation - support international business registration numbers (9-15 digits)
  if (formData.registrationId && formData.registrationId.trim()) {
    // Remove any non-digit characters for validation
    const digitsOnly = formData.registrationId.replace(/[^\d]/g, '')

    // Must have 9-15 digits to accommodate various international formats
    // Examples: US EIN (9), Canadian BIN (9), Australian ABN (11), etc.
    if (digitsOnly.length < 9 || digitsOnly.length > 15) {
      errors.push("Business registration number must be 9-15 digits")
    }

    // Optional: Check for common formats but don't enforce strict patterns
    // This allows flexibility for international formats while providing helpful feedback
    if (digitsOnly.length === 9 && !/^\d{2}-?\d{7}$/.test(formData.registrationId)) {
      // Suggest EIN format for 9-digit numbers but don't enforce
      console.log("Note: 9-digit number detected. Consider using XX-XXXXXXX format for U.S. EIN")
    }
  }

  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.push("Business email must be a valid email address")
  }

  if (formData.repEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.repEmail)) {
    errors.push("Representative email must be a valid email address")
  }

  if (formData.website && !/^https?:\/\/.+/.test(formData.website)) {
    errors.push("Website must be a valid HTTP/HTTPS URL")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Check if form has all required fields for Twilio API submission
 * This is separate from basic validation to allow saving incomplete forms to Supabase
 */
export function isFormCompleteForTwilioSubmission(formData: BusinessProfileFormData): boolean {
  const validation = validateFormData(formData)
  return validation.isValid && validation.errors.length === 0
}

/**
 * Validate if a field has sufficient data for auto-save
 */
function isValidForAutoSave(value: string | undefined, minLength: number = 1): boolean {
  return Boolean(value?.trim() && value.trim().length >= minLength)
}

/**
 * Validate email format for auto-save
 */
function isValidEmailForAutoSave(email: string | undefined): boolean {
  if (!email?.trim()) return false
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return emailPattern.test(email.trim())
}

/**
 * Validate phone format for auto-save
 */
function isValidPhoneForAutoSave(phone: string | undefined): boolean {
  if (!phone?.trim()) return false
  const digitsOnly = phone.replace(/\D/g, '')
  return digitsOnly.length >= 10 && digitsOnly.length <= 15
}

/**
 * Transform partial form data to auto-save request format
 * Only sends fields with valid data to prevent validation errors
 */
export function transformFormDataToAutoSaveRequest(formData: Partial<BusinessProfileFormData>): AutoSaveBusinessProfileRequest {
  const autoSaveData: AutoSaveBusinessProfileRequest = {}

  // Map only non-empty fields with proper validation
  if (isValidForAutoSave(formData.legalBusinessName)) {
    autoSaveData.company_name = formData.legalBusinessName!.trim()
  }

  if (formData.businessType) {
    autoSaveData.business_type = mapBusinessType(formData.businessType)
  }

  if (isValidForAutoSave(formData.registrationId)) {
    autoSaveData.business_registration_id = formData.registrationId!.trim()
    // Also set legacy field for backward compatibility
    autoSaveData.ein = formData.registrationId!.trim()
  }

  if (formData.registrationIdType) {
    // Map frontend display name to constraint-compliant backend code
    const registrationType = getRegistrationTypeByName(formData.registrationIdType);
    const backendCode = registrationType?.backendCode || formData.registrationIdType;

    // Use the correct field name for auto-save schema
    autoSaveData.business_registration_id_type = backendCode;
    // Also set legacy field for backward compatibility
    autoSaveData.registration_id_type = backendCode;
  }

  if (isValidForAutoSave(formData.website)) {
    autoSaveData.website = formData.website!.trim()
  }

  if (isValidForAutoSave(formData.industry)) {
    autoSaveData.vertical = formData.industry!.trim()
  }

  // Map regions for auto-save
  if (formData.regions && formData.regions.length > 0) {
    const mappedRegions = mapRegionsToBackend(formData.regions)
    autoSaveData.business_regions = mappedRegions
    autoSaveData.business_regions_of_operation = mappedRegions.join(', ')
  }

  // Social media URLs
  if (isValidForAutoSave(formData.socialMediaUrls)) {
    autoSaveData.social_media_profile_urls = formData.socialMediaUrls!.trim()
  }

  // Business identity (default for secondary customer profiles)
  autoSaveData.business_identity = 'direct_customer'

  // Handle nested address object - only create if we have valid fields
  const addressData: any = {}
  let hasValidAddressData = false

  if (isValidForAutoSave(formData.streetAddress)) {
    addressData.street = formData.streetAddress!.trim()
    hasValidAddressData = true
  }

  if (isValidForAutoSave(formData.city)) {
    addressData.city = formData.city!.trim()
    hasValidAddressData = true
  }

  if (isValidForAutoSave(formData.stateProvince, 2)) { // State requires min 2 characters
    addressData.state = formData.stateProvince!.trim()
    hasValidAddressData = true
  }

  if (isValidForAutoSave(formData.postalCode)) {
    addressData.postal_code = formData.postalCode!.trim()
    hasValidAddressData = true
  }

  if (formData.country) {
    const countryCode = getCountryCodeByName(formData.country)
    if (countryCode) {
      addressData.country = countryCode
      hasValidAddressData = true
    }
  }

  // Only include address if we have at least one valid field
  if (hasValidAddressData) {
    autoSaveData.address = addressData
  }

  // Handle nested representative object - only create if we have valid fields
  const repData: any = {}
  let hasValidRepData = false

  if (isValidForAutoSave(formData.repFirstName)) {
    repData.first_name = formData.repFirstName!.trim()
    hasValidRepData = true
  }

  if (isValidForAutoSave(formData.repLastName)) {
    repData.last_name = formData.repLastName!.trim()
    hasValidRepData = true
  }

  if (isValidEmailForAutoSave(formData.repEmail)) {
    repData.email = formData.repEmail!.trim()
    hasValidRepData = true
  }

  if (isValidPhoneForAutoSave(formData.repPhone)) {
    repData.phone = formData.repPhone!.trim()
    hasValidRepData = true
  }

  if (isValidForAutoSave(formData.repJobTitle)) {
    repData.job_title = formData.repJobTitle!.trim()
    hasValidRepData = true
  }

  // Only include representative if we have at least one valid field
  if (hasValidRepData) {
    autoSaveData.representative = repData
  }

  return autoSaveData
}
