-- Auto-save Field Mapping Test Script (SQL)
-- Tests Phase 1 changes to verify auto-save stores data only in newer field names

-- Step 1: Get a valid subaccount_sid for testing
DO $$
DECLARE
    test_subaccount_sid VARCHAR(34);
BEGIN
    -- Get the first available subaccount_sid
    SELECT subaccount_sid INTO test_subaccount_sid 
    FROM twilio_subaccounts 
    LIMIT 1;
    
    IF test_subaccount_sid IS NULL THEN
        RAISE EXCEPTION 'No subaccount_sid found in twilio_subaccounts table. Please create a test subaccount first.';
    END IF;
    
    RAISE NOTICE 'Using test subaccount_sid: %', test_subaccount_sid;
    
    -- Clean up any existing test data
    DELETE FROM trust_hub_customer_profiles 
    WHERE subaccount_sid = test_subaccount_sid;
    
    RAISE NOTICE 'Cleaned up existing test data';
END $$;

-- Step 2: Test 1 - Simulate auto-save storing data in newer field names only
DO $$
DECLARE
    test_subaccount_sid VARCHAR(34);
BEGIN
    -- Get the test subaccount_sid
    SELECT subaccount_sid INTO test_subaccount_sid 
    FROM twilio_subaccounts 
    LIMIT 1;
    
    -- Insert data using ONLY newer field names (simulating auto-save behavior)
    INSERT INTO trust_hub_customer_profiles (
        subaccount_sid,
        business_name,
        business_registration_number,        -- Newer field
        street_address,                      -- Newer field  
        representative_first_name,           -- Newer field
        representative_last_name,            -- Newer field
        representative_email,                -- Newer field
        representative_phone,                -- Newer field
        representative_job_title,            -- Newer field
        rep_business_title,                  -- Only exists in legacy format
        status,
        profile_type,
        updated_at
    ) VALUES (
        test_subaccount_sid,
        'Auto-save Test Company',
        'AUTOSAVE123456789',                 -- Should be in business_registration_number
        '123 Auto-save Street',              -- Should be in street_address
        'John',                              -- Should be in representative_first_name
        'Doe',                               -- Should be in representative_last_name
        '<EMAIL>',             -- Should be in representative_email
        '+**********',                       -- Should be in representative_phone
        'CEO',                               -- Should be in representative_job_title
        'Chief Executive Officer',           -- Should be in rep_business_title
        'draft',
        'secondary',
        NOW()
    ) ON CONFLICT (subaccount_sid) DO UPDATE SET
        business_name = EXCLUDED.business_name,
        business_registration_number = EXCLUDED.business_registration_number,
        street_address = EXCLUDED.street_address,
        representative_first_name = EXCLUDED.representative_first_name,
        representative_last_name = EXCLUDED.representative_last_name,
        representative_email = EXCLUDED.representative_email,
        representative_phone = EXCLUDED.representative_phone,
        representative_job_title = EXCLUDED.representative_job_title,
        rep_business_title = EXCLUDED.rep_business_title,
        updated_at = EXCLUDED.updated_at;
    
    RAISE NOTICE 'Test 1: Inserted auto-save data using newer field names only';
END $$;

-- Step 3: Verify Test 1 Results
DO $$
DECLARE
    test_subaccount_sid VARCHAR(34);
    test_record RECORD;
    test_passed BOOLEAN := TRUE;
BEGIN
    -- Get the test subaccount_sid
    SELECT subaccount_sid INTO test_subaccount_sid 
    FROM twilio_subaccounts 
    LIMIT 1;
    
    -- Get the test record
    SELECT * INTO test_record 
    FROM trust_hub_customer_profiles 
    WHERE subaccount_sid = test_subaccount_sid;
    
    RAISE NOTICE '=== Test 1 Results: Auto-save Field Storage ===';
    
    -- Check newer fields have data
    IF test_record.business_registration_number = 'AUTOSAVE123456789' THEN
        RAISE NOTICE '✅ business_registration_number: % (correct)', test_record.business_registration_number;
    ELSE
        RAISE NOTICE '❌ business_registration_number: % (should be AUTOSAVE123456789)', test_record.business_registration_number;
        test_passed := FALSE;
    END IF;
    
    IF test_record.street_address = '123 Auto-save Street' THEN
        RAISE NOTICE '✅ street_address: % (correct)', test_record.street_address;
    ELSE
        RAISE NOTICE '❌ street_address: % (should be 123 Auto-save Street)', test_record.street_address;
        test_passed := FALSE;
    END IF;
    
    IF test_record.representative_first_name = 'John' THEN
        RAISE NOTICE '✅ representative_first_name: % (correct)', test_record.representative_first_name;
    ELSE
        RAISE NOTICE '❌ representative_first_name: % (should be John)', test_record.representative_first_name;
        test_passed := FALSE;
    END IF;
    
    -- Check older fields are NULL
    IF test_record.business_registration_id IS NULL THEN
        RAISE NOTICE '✅ business_registration_id: NULL (correct)';
    ELSE
        RAISE NOTICE '❌ business_registration_id: % (should be NULL)', test_record.business_registration_id;
        test_passed := FALSE;
    END IF;
    
    IF test_record.street IS NULL THEN
        RAISE NOTICE '✅ street: NULL (correct)';
    ELSE
        RAISE NOTICE '❌ street: % (should be NULL)', test_record.street;
        test_passed := FALSE;
    END IF;
    
    IF test_record.rep_first_name IS NULL THEN
        RAISE NOTICE '✅ rep_first_name: NULL (correct)';
    ELSE
        RAISE NOTICE '❌ rep_first_name: % (should be NULL)', test_record.rep_first_name;
        test_passed := FALSE;
    END IF;
    
    IF test_passed THEN
        RAISE NOTICE '🎯 Test 1: ✅ PASSED - Auto-save stores data only in newer field names';
    ELSE
        RAISE NOTICE '🎯 Test 1: ❌ FAILED - Auto-save is not storing data correctly';
    END IF;
END $$;

-- Step 4: Test 2 - Backward compatibility (insert data in old fields, verify retrieval)
DO $$
DECLARE
    test_subaccount_sid VARCHAR(34);
BEGIN
    -- Get the test subaccount_sid
    SELECT subaccount_sid INTO test_subaccount_sid 
    FROM twilio_subaccounts 
    LIMIT 1;
    
    -- Clean up previous test
    DELETE FROM trust_hub_customer_profiles 
    WHERE subaccount_sid = test_subaccount_sid;
    
    -- Insert data using OLD field names (simulating legacy data)
    INSERT INTO trust_hub_customer_profiles (
        subaccount_sid,
        business_name,
        business_registration_id,            -- Old field
        street,                              -- Old field
        rep_first_name,                      -- Old field
        rep_last_name,                       -- Old field
        rep_email,                           -- Old field
        rep_phone,                           -- Old field
        rep_job_position,                    -- Old field
        rep_business_title,                  -- Legacy field
        status,
        profile_type,
        updated_at
    ) VALUES (
        test_subaccount_sid,
        'Legacy Test Company',
        'LEGACY123456789',                   -- Should be readable from business_registration_id
        '456 Legacy Street',                 -- Should be readable from street
        'Jane',                              -- Should be readable from rep_first_name
        'Smith',                             -- Should be readable from rep_last_name
        '<EMAIL>',             -- Should be readable from rep_email
        '+**********',                       -- Should be readable from rep_phone
        'CTO',                               -- Should be readable from rep_job_position
        'Chief Technology Officer',          -- Should be readable from rep_business_title
        'draft',
        'secondary',
        NOW()
    );
    
    RAISE NOTICE 'Test 2: Inserted legacy data using old field names';
END $$;

-- Step 5: Verify Test 2 Results (simulate retrieval logic)
DO $$
DECLARE
    test_subaccount_sid VARCHAR(34);
    test_record RECORD;
    retrieved_reg_number VARCHAR(100);
    retrieved_street VARCHAR(255);
    retrieved_first_name VARCHAR(100);
    retrieved_last_name VARCHAR(100);
    retrieved_email VARCHAR(255);
    test_passed BOOLEAN := TRUE;
BEGIN
    -- Get the test subaccount_sid
    SELECT subaccount_sid INTO test_subaccount_sid 
    FROM twilio_subaccounts 
    LIMIT 1;
    
    -- Get the test record
    SELECT * INTO test_record 
    FROM trust_hub_customer_profiles 
    WHERE subaccount_sid = test_subaccount_sid;
    
    RAISE NOTICE '=== Test 2 Results: Backward Compatibility ===';
    
    -- Simulate retrieval logic (prefer newer fields, fallback to older)
    retrieved_reg_number := COALESCE(test_record.business_registration_number, test_record.business_registration_id);
    retrieved_street := COALESCE(test_record.street_address, test_record.street);
    retrieved_first_name := COALESCE(test_record.representative_first_name, test_record.rep_first_name);
    retrieved_last_name := COALESCE(test_record.representative_last_name, test_record.rep_last_name);
    retrieved_email := COALESCE(test_record.representative_email, test_record.rep_email);
    
    -- Verify retrieval works correctly
    IF retrieved_reg_number = 'LEGACY123456789' THEN
        RAISE NOTICE '✅ Registration number retrieved: % (from old field)', retrieved_reg_number;
    ELSE
        RAISE NOTICE '❌ Registration number: % (should be LEGACY123456789)', retrieved_reg_number;
        test_passed := FALSE;
    END IF;
    
    IF retrieved_street = '456 Legacy Street' THEN
        RAISE NOTICE '✅ Street address retrieved: % (from old field)', retrieved_street;
    ELSE
        RAISE NOTICE '❌ Street address: % (should be 456 Legacy Street)', retrieved_street;
        test_passed := FALSE;
    END IF;
    
    IF retrieved_first_name = 'Jane' THEN
        RAISE NOTICE '✅ First name retrieved: % (from old field)', retrieved_first_name;
    ELSE
        RAISE NOTICE '❌ First name: % (should be Jane)', retrieved_first_name;
        test_passed := FALSE;
    END IF;
    
    IF test_passed THEN
        RAISE NOTICE '🎯 Test 2: ✅ PASSED - Backward compatibility works correctly';
    ELSE
        RAISE NOTICE '🎯 Test 2: ❌ FAILED - Backward compatibility is broken';
    END IF;
END $$;

-- Step 6: Cleanup
DO $$
DECLARE
    test_subaccount_sid VARCHAR(34);
BEGIN
    -- Get the test subaccount_sid
    SELECT subaccount_sid INTO test_subaccount_sid 
    FROM twilio_subaccounts 
    LIMIT 1;
    
    -- Clean up test data
    DELETE FROM trust_hub_customer_profiles 
    WHERE subaccount_sid = test_subaccount_sid;
    
    RAISE NOTICE '🧹 Test data cleaned up';
    RAISE NOTICE '✅ Auto-save field mapping tests completed';
END $$;
