# Updated Polling Behavior Summary

## ✅ **CHANGES IMPLEMENTED**

### **Polling Interval Updated**
- **Changed from**: 2 minutes (120000ms)
- **Changed to**: 5 minutes (300000ms)
- **Location**: `frontend/components/secondary-customer-profile-section.tsx` line 95

### **Polling Endpoint Updated**
- **Changed from**: Database-only query (`refetchTrustHubStatus()`)
- **Changed to**: Twilio API query with database update (`pollingRefreshMutation.mutate()`)
- **Location**: `frontend/components/secondary-customer-profile-section.tsx` line 93

### **Toast Notification Control Added**
- **Manual refresh**: Shows toast notifications for user feedback
- **Automatic polling**: Silent operation with console logging only
- **Location**: `frontend/hooks/use-business-profile.ts` lines 242-293

---

## 🔄 **NEW POLLING FLOW**

### **Complete Data Flow**
```
User Opens TARA Modal → enabled=true
    ↓
Trust Hub Status = 'pending-review' or 'in-review' → Start 5-minute polling
    ↓
Frontend (every 5 minutes) → Backend API (with JWT) → Twilio Trust Hub API
    ↓
Twilio API Response → Compare with Supabase status → Update if different
    ↓
Backend Response → Frontend (silent console logging)
    ↓
User Closes TARA Modal → enabled=false → Stop polling
```

### **API Endpoint Called**
- **Endpoint**: `GET /api/v1/tara/business-profile/trust-hub-status/refresh`
- **Authentication**: JWT Bearer token required
- **Twilio API**: `customer_profiles(customer_profile_sid).fetch()`
- **Database Update**: Only if status changed from Twilio response

---

## 🎯 **POLLING BEHAVIOR DETAILS**

### **When Polling Starts**
1. ✅ User opens TARA modal (`enabled={open}`)
2. ✅ Trust Hub status is pending (`pending-review` or `in-review`)
3. ✅ Both conditions must be true

### **What Happens During Polling**
1. **Frontend**: Calls refresh mutation every 5 minutes
2. **Backend**: Queries Twilio Trust Hub API for live status
3. **Database**: Updates Supabase if status changed
4. **Frontend**: Receives updated status (silent logging)

### **When Polling Stops**
1. ❌ User closes TARA modal (`enabled=false`)
2. ❌ Trust Hub status changes to non-pending (`twilio-approved`, `twilio-rejected`, `draft`)
3. ❌ Component unmounts (automatic cleanup)

---

## 🔧 **IMPLEMENTATION DETAILS**

### **Frontend Changes**
**File**: `frontend/components/secondary-customer-profile-section.tsx`

```typescript
// Two separate hooks for different use cases
const refreshTrustHubMutation = useRefreshTrustHubCustomerProfileStatus({ showToast: true })  // Manual
const pollingRefreshMutation = useRefreshTrustHubCustomerProfileStatus({ showToast: false }) // Automatic

// Polling logic
if (enabled && isPendingState) {
  pollingIntervalRef.current = setInterval(() => {
    console.log('🔄 Polling Twilio API for Trust Hub status updates...')
    pollingRefreshMutation.mutate() // Queries Twilio API
  }, 300000) // 5 minutes
}
```

### **Hook Enhancement**
**File**: `frontend/hooks/use-business-profile.ts`

```typescript
export function useRefreshTrustHubCustomerProfileStatus(options?: { showToast?: boolean }) {
  const showToast = options?.showToast ?? true

  return useMutation({
    onSuccess: (data) => {
      if (showToast) {
        // Show toast for manual refresh
        toast({ title: "Status Updated!", ... })
      } else {
        // Silent logging for automatic polling
        console.log(`🔄 Trust Hub status updated: ${data.data.status}`)
      }
    }
  })
}
```

---

## 📊 **COMPARISON: Before vs After**

### **Before (Database Polling)**
```
Frontend (every 2 minutes) → Backend → Supabase Database → Return cached status
```
- **Pros**: Fast response, low API usage
- **Cons**: Status could be outdated, relies on webhooks

### **After (Twilio API Polling)**
```
Frontend (every 5 minutes) → Backend → Twilio API → Update Supabase → Return live status
```
- **Pros**: Always current status, automatic database updates
- **Cons**: Higher API usage, slightly slower response

---

## 🎯 **BENEFITS ACHIEVED**

### **1. Real-time Status Updates**
- Polling now gets live status directly from Twilio
- No waiting for webhooks during pending states
- Immediate detection of status changes

### **2. Automatic Database Synchronization**
- Database automatically updated when status changes
- Ensures consistency between Twilio and Supabase
- No manual intervention required

### **3. Controlled API Usage**
- Only polls when user is actively viewing TARA modal
- Only polls during pending states (not for completed profiles)
- 5-minute interval prevents excessive API calls
- Proper authentication on all requests

### **4. Enhanced User Experience**
- Manual refresh: Immediate feedback with toast notifications
- Automatic polling: Silent operation without interrupting user
- Consistent status display across all components

---

## 🔍 **MONITORING & LOGGING**

### **Console Logs**
```javascript
// Polling start
"✅ Started polling Twilio API for Trust Hub status updates (5-minute interval)"

// During polling
"🔄 Polling Twilio API for Trust Hub status updates..."
"🔄 Trust Hub status updated: pending-review → twilio-approved"

// Polling stop
"⏹️ Stopped polling Twilio API for Trust Hub status updates"
```

### **API Call Pattern**
- **Frequency**: Every 5 minutes when conditions met
- **Authentication**: JWT Bearer token
- **Endpoint**: `/api/v1/tara/business-profile/trust-hub-status/refresh`
- **Twilio API**: `customer_profiles().fetch()`

---

## ✅ **VERIFICATION CHECKLIST**

- [x] Polling interval changed to 5 minutes
- [x] Polling queries Twilio API (not just database)
- [x] Database updated automatically if status changes
- [x] Proper JWT authentication on all requests
- [x] Only polls when TARA modal is open
- [x] Only polls during pending states
- [x] Silent operation for automatic polling
- [x] Toast notifications for manual refresh
- [x] Automatic cleanup when conditions change

The polling behavior now perfectly matches your requirements: **Frontend → Backend → Twilio API → Update Supabase if different → Return result**, with proper authentication and controlled frequency.
